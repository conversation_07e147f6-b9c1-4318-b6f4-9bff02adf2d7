import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  Text,
  TouchableOpacity,
  Dimensions,
  TextInput
} from 'react-native';
import { IconButton } from 'react-native-paper';
import { useTheme } from 'react-native-paper';
import axios from 'axios';
import { io } from 'socket.io-client';
import { useAuth, API_BASE_URL } from '../../context/AuthContext';
import ChatMessage from '../../components/ChatMessage';
import ChatInput from '../../components/ChatInput';
import { Message, Attachment } from '../../types/chat';
import { RouteProp } from '@react-navigation/native';
import { transformPatients } from '../../utils/dataTransformers';

type CounsellorChatScreenProps = {
  route: RouteProp<{
    params: {
      patientId?: string;
    }
  }, 'params'>;
  navigation: any;
};

const CounsellorChatScreen: React.FC<CounsellorChatScreenProps> = ({ route, navigation }) => {
  const theme = useTheme();
  const { user } = useAuth();

  const [messages, setMessages] = useState<Message[]>([]);
  const [selectedPatient, setSelectedPatient] = useState<string | null>(null);

  // Track unread messages for each patient
  const [unreadMessages, setUnreadMessages] = useState<{ [key: string]: number }>({});

  const [patientSearchTerm, setPatientSearchTerm] = useState('');
  const [socket, setSocket] = useState<any>(null);
  const [patients, setPatients] = useState<any[]>([]);

  const [isUserAtBottom, setIsUserAtBottom] = useState(true);
  const flatListRef = useRef<FlatList>(null);

  const screenWidth = Dimensions.get('window').width;
  const isMobile = screenWidth < 600;

  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const newSocket = io(API_BASE_URL);
    setSocket(newSocket);

    const fetchPatients = async () => {
      try {
        const response = await axios.get(`${API_BASE_URL}/api/patients`);
        // Transform MongoDB documents to standardized objects with 'id' property
        const transformedPatients = transformPatients(response.data);
        setPatients(transformedPatients);

        // If patientId is provided in route params, set it as the selected patient
        if (route.params?.patientId) {
          setSelectedPatient(route.params.patientId);
        }
      } catch (error) {
        console.error('Error fetching patients:', error);
      }
    };

    fetchPatients();

    return () => {
      if (newSocket) {
        newSocket.disconnect();
      }
    };
  }, [route.params?.patientId]);

useEffect(() => {
  if (!socket || !user?.id) return;

  socket.emit('join-room', user.id);

  socket.on('receive-message', (message: any) => {
    setMessages((prev) => {
      if (!prev.some((m) => m.id === message.id)) {
        return [...prev, message];
      }
      return prev;
    });

    // Update unread message count for the relevant patient, but only if the message is for the current user
    if (message.receiverId === user.id && message.senderId !== selectedPatient) {
      setUnreadMessages((prev) => {
        const count = prev[message.senderId] || 0;
        return {
          ...prev,
          [message.senderId]: count + 1,
        };
      });
    }

    if (isUserAtBottom) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  });

  return () => {
    socket.off('receive-message');
  };
}, [socket, user, isUserAtBottom]);

useEffect(() => {
  if (!selectedPatient || !user?.id) return;

  const fetchChatHistory = async () => {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/api/chat/history/${user.id}/${selectedPatient}`
      );

      const formatted = response.data.map((msg: any) => ({
        id: msg._id,
        text: msg.content,
        timestamp: msg.timestamp,
        senderId: msg.senderId._id,
        senderName: msg.senderId.name,
        attachment: msg.attachment || null,
      }));

      setMessages(formatted.reverse());

      // Mark as read when selecting a patient and reset unread messages for this patient
      setUnreadMessages((prev) => ({
        ...prev,
        [selectedPatient]: 0, // Reset unread count for this patient
      }));

      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: false });
      }, 100);
    } catch (error) {
      console.error('Error fetching chat history:', error);
    }
  };

  fetchChatHistory();
}, [selectedPatient, user]);


  const handleScroll = (event: any) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const isAtBottom = layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;
    setIsUserAtBottom(isAtBottom);
  };

  const handleSend = async (message: string, attachment?: Attachment) => {
    if (!selectedPatient || !user?.id || !socket) return;

    try {
      const messageData: any = {
        sender: user.id,
        receiver: selectedPatient,
        content: message
      };

      // Add attachment if present
      if (attachment) {
        messageData.attachment = attachment;
      }

      const response = await axios.post(`${API_BASE_URL}/api/chat/send`, messageData);

      socket.emit('send-message', {
        id: response.data._id,
        text: message,
        attachment: attachment || null,
        timestamp: new Date().toISOString(),
        senderId: user.id,
        senderName: user.name
      });
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const filteredPatients = patients.filter((p) =>
    p.name.toLowerCase().includes(patientSearchTerm.toLowerCase())
  );

  const displayedMessages = messages.filter((msg) =>
    msg.text.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {isMobile ? (
        selectedPatient ? (
          <View style={styles.fullWidthChatContainer}>
            <View style={styles.chatHeader}>
              <IconButton
                icon="arrow-left"
                onPress={() => {
                  if (route.params?.patientId) {
                    // If we came from patient list, go back to it
                    navigation.goBack();
                  } else {
                    // Otherwise just clear the selected patient
                    setSelectedPatient(null);
                  }
                }}
                size={24}
              />
              <Text style={styles.selectedPatientName}>
                {patients.find((p) => p.id === selectedPatient)?.name || 'Patient Name'}
              </Text>
              <View style={styles.searchContainer}>
                {isSearchExpanded ? (
                  <View style={styles.searchInputContainer}>
                    <TextInput
                      style={styles.searchInput}
                      placeholder="Search messages..."
                      value={searchTerm}
                      onChangeText={setSearchTerm}
                      autoFocus
                    />
                    <IconButton
                      icon="close"
                      onPress={() => {
                        setSearchTerm('');
                        setIsSearchExpanded(false);
                      }}
                      size={20}
                    />
                  </View>
                ) : (
                  <IconButton
                    icon="magnify"
                    onPress={() => setIsSearchExpanded(true)}
                    size={24}
                  />
                )}
              </View>
            </View>

            <FlatList
              ref={flatListRef}
              data={displayedMessages}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <ChatMessage
                  message={item.text}
                  isCurrentUser={item.senderId === user?.id}
                  timestamp={item.timestamp}
                  senderName={item.senderName}
                  attachment={item.attachment}
                />
              )}
              contentContainerStyle={styles.messagesContainer}
              onScroll={handleScroll}
            />
            <ChatInput onSend={handleSend} />
          </View>
        ) : (
          <View style={styles.fullWidthPatientList}>
            <View style={styles.patientSearchContainer}>
              <TextInput
                style={styles.patientSearchInput}
                placeholder="Search patients..."
                value={patientSearchTerm}
                onChangeText={setPatientSearchTerm}
              />
              {patientSearchTerm.length > 0 && (
                <Text style={styles.patientCount}>
                  {filteredPatients.length} patients found
                </Text>
              )}
            </View>

            <FlatList
              data={filteredPatients}
              keyExtractor={(item) => item.id}
              renderItem={({ item: patient }) => (
                <TouchableOpacity
                  style={[styles.patientItem, selectedPatient === patient.id && styles.selectedPatient]}
                  onPress={() => setSelectedPatient(patient.id)}
                >
                  <Text style={styles.patientName}>{patient.name}</Text>
                  {unreadMessages[patient.id] > 0 && (
                    <View style={styles.badge}>
                      <Text style={styles.badgeText}>{unreadMessages[patient.id]}</Text>
                    </View>
                  )}
                </TouchableOpacity>
              )}
              contentContainerStyle={styles.patientList}
            />
          </View>
        )
      ) : (
        <>
          <View style={styles.leftPanel}>
            <View style={styles.patientSearchContainer}>
              <TextInput
                style={styles.patientSearchInput}
                placeholder="Search patients..."
                value={patientSearchTerm}
                onChangeText={setPatientSearchTerm}
              />
              {patientSearchTerm.length > 0 && (
                <Text style={styles.patientCount}>
                  {filteredPatients.length} patients found
                </Text>
              )}
            </View>

            <FlatList
              data={filteredPatients}
              keyExtractor={(item) => item.id}
              renderItem={({ item: patient }) => (
                <TouchableOpacity
                  style={[styles.patientItem, selectedPatient === patient.id && styles.selectedPatient]}
                  onPress={() => setSelectedPatient(patient.id)}
                >
                  <Text style={styles.patientName}>{patient.name}</Text>
                  {unreadMessages[patient.id] > 0 && (
                    <View style={styles.badge}>
                      <Text style={styles.badgeText}>{unreadMessages[patient.id]}</Text>
                    </View>
                  )}
                </TouchableOpacity>
              )}
              contentContainerStyle={styles.patientList}
            />
          </View>

          <View style={styles.rightPanel}>
            {selectedPatient ? (
              <>
                <View style={styles.chatHeader}>
                  {route.params?.patientId && (
                    <IconButton
                      icon="arrow-left"
                      onPress={() => navigation.goBack()}
                      size={24}
                      style={{ marginRight: 8 }}
                    />
                  )}
                  <Text style={styles.selectedPatientName}>
                    {patients.find((p) => p.id === selectedPatient)?.name || 'Patient Name'}
                  </Text>
                  <View style={styles.searchContainer}>
                    {isSearchExpanded ? (
                      <View style={styles.searchInputContainer}>
                        <TextInput
                          style={styles.searchInput}
                          placeholder="Search messages..."
                          value={searchTerm}
                          onChangeText={setSearchTerm}
                          autoFocus
                        />
                        <IconButton
                          icon="close"
                          onPress={() => {
                            setSearchTerm('');
                            setIsSearchExpanded(false);
                          }}
                          size={20}
                        />
                      </View>
                    ) : (
                      <IconButton
                        icon="magnify"
                        onPress={() => setIsSearchExpanded(true)}
                        size={24}
                      />
                    )}
                  </View>
                </View>

                <FlatList
                  ref={flatListRef}
                  data={displayedMessages}
                  keyExtractor={(item) => item.id}
                  renderItem={({ item }) => (
                    <ChatMessage
                      message={item.text}
                      isCurrentUser={item.senderId === user?.id}
                      timestamp={item.timestamp}
                      senderName={item.senderName}
                      attachment={item.attachment}
                    />
                  )}
                  contentContainerStyle={styles.messagesContainer}
                  onScroll={handleScroll}
                />
                <ChatInput onSend={handleSend} />
              </>
            ) : (
              <View style={styles.noPatientSelected}>
                <Text>Select a patient to start chatting</Text>
              </View>
            )}
          </View>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  leftPanel: {
    width: '30%',
    padding: 16
  },
  rightPanel: {
    flex: 1,
    padding: 16
  },
  patientSearchContainer: {
    marginBottom: 16
  },
  patientSearchInput: {
    padding: 8,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    marginBottom: 10
  },
  patientCount: {
    color: '#888'
  },
  patientList: {
    paddingBottom: 16
  },
  patientItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 12,
    backgroundColor: '#f9f9f9',
    marginVertical: 5,
    borderRadius: 8
  },
  selectedPatient: {
    backgroundColor: '#e0e0e0'
  },
  patientName: {
    fontSize: 16
  },
  badge: {
    backgroundColor: '#ff0000',
    borderRadius: 12,
    paddingVertical: 2,
    paddingHorizontal: 8
  },
  badgeText: {
    color: '#fff',
    fontSize: 12
  },
  fullWidthChatContainer: {
    flex: 1
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  selectedPatientName: {
    fontSize: 18,
    fontWeight: 'bold'
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8
  },
  searchInput: {
    borderBottomWidth: 1,
    borderColor: '#ccc',
    padding: 8,
    width: 200
  },
  searchInputIcon: {
    marginLeft: 8
  },
  noPatientSelected: {
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%'
  },
  // Add missing styles
  messagesContainer: {
    paddingBottom: 16,
    paddingTop: 8
  },
  fullWidthPatientList: {
    flex: 1,
    padding: 16
  }
});


export default CounsellorChatScreen;
