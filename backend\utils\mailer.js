// utils/mailer.js
const nodemailer = require('nodemailer');
require('dotenv').config({ path: '../.env' });

// Create a transporter for Gmail SMTP
const transporter = nodemailer.createTransport({
  host: 'smtp.gmail.com',
  port: 587,
  secure: false,
  auth: {
    user: process.env.GMAIL_USER,
    pass: process.env.GMAIL_PASS,
  },
});

// Verify connection configuration
transporter.verify((error, success) => {
  if (error) {
    console.log('Email server connection failed:', error);
  } else {
    console.log('Email server is ready');
  }
});

/**
 * Sends an email with optional HTML support.
 * @param {Object} options - Email options
 * @param {string} options.to - Recipient email
 * @param {string} options.subject - Email subject
 * @param {string} [options.text] - Plain text body (fallback)
 * @param {string} [options.html] - HTML body (for rich content)
 */
const sendEmail = async ({ to, subject, text, html }) => {
  try {
    await transporter.sendMail({
      from: `"ADR App" <${process.env.GMAIL_USER}>`,
      to,
      subject,
      text,
      html,
    });
    console.log(`✅ Email sent to ${to}`);
  } catch (err) {
    console.error('❌ Email sending error:', err);
  }
};

module.exports = sendEmail;
