import React, { useState } from 'react';
import { View, TextInput, StyleSheet, TouchableOpacity, Platform, Image, Text } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme, Portal, Modal, Button, IconButton } from 'react-native-paper';
import { ChatInputProps, Attachment } from '../types/chat';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';

const ChatInput: React.FC<ChatInputProps> = ({ onSend }) => {
  const [message, setMessage] = useState('');
  const [attachment, setAttachment] = useState<Attachment | null>(null);
  const [showAttachmentModal, setShowAttachmentModal] = useState(false);
  const theme = useTheme();

  const handleSend = () => {
    const trimmedMessage = message.trim();
    if (trimmedMessage || attachment) {
      onSend(trimmedMessage, attachment || undefined);
      setMessage('');
      setAttachment(null);
    }
  };

  const handleKeyPress = ({ nativeEvent }: { nativeEvent: any }) => {
    if (nativeEvent.key === 'Enter' && !nativeEvent.shiftKey) {
      handleSend();
      nativeEvent.preventDefault();
    }
  };

  const pickImage = async () => {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (!permissionResult.granted) {
        alert('Permission to access camera roll is required!');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        base64: true,
        exif: true,  // Get additional metadata if available
      });

      if (!result.canceled && result.assets && result.assets[0]) {
        const asset = result.assets[0];
        const fileSize = asset.fileSize || 0;

        // Check file size (10MB limit)
        if (fileSize > 10 * 1024 * 1024) {
          alert('File size exceeds 10MB limit');
          return;
        }

        // Extract file name and extension
        let originalFileName = '';
        let fileExtension = '';

        // Try to get the file name from the URI
        if (asset.uri) {
          const uriParts = asset.uri.split('/');
          originalFileName = uriParts[uriParts.length - 1];

          // Extract extension from file name
          if (originalFileName && originalFileName.includes('.')) {
            const nameParts = originalFileName.split('.');
            fileExtension = nameParts[nameParts.length - 1].toLowerCase();
          }
        }

        // If we couldn't get a proper file name or extension, try to determine from MIME type
        if (!fileExtension || fileExtension === '') {
          // Try to get extension from MIME type if available
          if (asset.type) {
            fileExtension = asset.type.split('/')[1] || 'jpg';
          } else {
            // Default to jpg
            fileExtension = 'jpg';
          }
        }

        // If we still don't have a file name, create one
        if (!originalFileName || !originalFileName.includes('.')) {
          originalFileName = `image_${new Date().getTime()}.${fileExtension}`;
        }

        // Determine MIME type based on extension
        let mimeType = 'image/jpeg'; // Default
        if (fileExtension === 'png') mimeType = 'image/png';
        if (fileExtension === 'gif') mimeType = 'image/gif';
        if (fileExtension === 'webp') mimeType = 'image/webp';
        if (fileExtension === 'jpg' || fileExtension === 'jpeg') mimeType = 'image/jpeg';

        setAttachment({
          fileType: 'image',
          fileName: originalFileName,
          fileSize: fileSize,
          fileData: asset.base64 || '',
          mimeType: mimeType,
        });

        setShowAttachmentModal(false);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      alert('Failed to pick image');
    }
  };

  const pickDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'text/plain'
        ],
        copyToCacheDirectory: true,
      });

      if (result.canceled === false && result.assets && result.assets[0]) {
        const asset = result.assets[0];
        const fileSize = asset.size || 0;

        // Check file size (10MB limit)
        if (fileSize > 10 * 1024 * 1024) {
          alert('File size exceeds 10MB limit');
          return;
        }

        // Read file as base64
        let fileData = '';
        if (Platform.OS === 'web') {
          // For web, we need to read the file as base64
          try {
            const response = await fetch(asset.uri);
            const blob = await response.blob();
            fileData = await new Promise((resolve, reject) => {
              const reader = new FileReader();
              reader.onloadend = () => {
                if (typeof reader.result === 'string') {
                  // Remove data URL prefix (e.g., "data:application/pdf;base64,")
                  const base64Data = reader.result.split(',')[1];
                  resolve(base64Data);
                } else {
                  reject(new Error('Failed to read file as base64'));
                }
              };
              reader.onerror = () => {
                reject(new Error('Error reading file'));
              };
              reader.readAsDataURL(blob);
            });
          } catch (err) {
            console.error('Error reading file:', err);
            alert('Could not read the file. Please try again.');
            return;
          }
        } else {
          try {
            // For Expo, we can use FileSystem to read the file
            const FileSystem = require('expo-file-system');
            const base64 = await FileSystem.readAsStringAsync(asset.uri, {
              encoding: FileSystem.EncodingType.Base64,
            });
            fileData = base64;
          } catch (err) {
            console.error('Error reading file:', err);
            alert('Could not read the file. Please try again.');
            return;
          }
        }

        // Determine file type based on mime type
        let fileType = 'document';
        if (asset.mimeType?.startsWith('image/')) {
          fileType = 'image';
        } else if (asset.mimeType?.startsWith('video/')) {
          fileType = 'video';
        } else if (asset.mimeType?.startsWith('audio/')) {
          fileType = 'audio';
        }

        setAttachment({
          fileType: fileType as any,
          fileName: asset.name,
          fileSize: fileSize,
          fileData: fileData,
          mimeType: asset.mimeType || 'application/octet-stream',
        });

        setShowAttachmentModal(false);
      }
    } catch (error) {
      console.error('Error picking document:', error);
      alert('Failed to pick document');
    }
  };

  const removeAttachment = () => {
    setAttachment(null);
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.attachmentButton}
        onPress={() => setShowAttachmentModal(true)}
      >
        <MaterialIcons
          name="attach-file"
          size={24}
          color={theme.colors.primary}
        />
      </TouchableOpacity>

      {attachment && (
        <View style={styles.attachmentPreview}>
          {attachment.fileType === 'image' ? (
            <Image
              source={{ uri: `data:${attachment.mimeType};base64,${attachment.fileData}` }}
              style={styles.imagePreview}
            />
          ) : (
            <View style={styles.documentPreview}>
              <MaterialIcons name="description" size={20} color={theme.colors.primary} />
              <Text style={styles.documentName} numberOfLines={1}>
                {attachment.fileName}
              </Text>
            </View>
          )}
          <TouchableOpacity onPress={removeAttachment} style={styles.removeButton}>
            <MaterialIcons name="close" size={16} color="#fff" />
          </TouchableOpacity>
        </View>
      )}

      <TextInput
        style={[
          styles.input,
          {
            backgroundColor: theme.colors.surface,
            color: theme.dark ? '#fff' : '#000',
          },
          attachment ? styles.inputWithAttachment : null
        ]}
        placeholder="Type a message..."
        value={message}
        onChangeText={setMessage}
        multiline
        onKeyPress={handleKeyPress}
      />

      <TouchableOpacity
        style={styles.sendButton}
        onPress={handleSend}
        disabled={!message.trim() && !attachment}
      >
        <MaterialIcons
          name="send"
          size={24}
          color={(message.trim() || attachment) ? theme.colors.primary : '#ccc'}
        />
      </TouchableOpacity>

      <Portal>
        <Modal
          visible={showAttachmentModal}
          onDismiss={() => setShowAttachmentModal(false)}
          contentContainerStyle={styles.modalContainer}
        >
          <Text style={styles.modalTitle}>Add Attachment</Text>

          <Button
            mode="outlined"
            icon="image"
            onPress={pickImage}
            style={styles.modalButton}
          >
            Image
          </Button>

          <Button
            mode="outlined"
            icon="file-document"
            onPress={pickDocument}
            style={styles.modalButton}
          >
            Document
          </Button>

          <Button
            mode="text"
            onPress={() => setShowAttachmentModal(false)}
            style={styles.cancelButton}
          >
            Cancel
          </Button>
        </Modal>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderTopWidth: 1,
    borderTopColor: '#e1e1e1',
    gap: 8,
    flexWrap: 'wrap',
  },
  attachmentButton: {
    padding: 8,
  },
  input: {
    flex: 1,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    maxHeight: 100,
  },
  inputWithAttachment: {
    maxWidth: '70%',
  },
  sendButton: {
    padding: 8,
  },
  attachmentPreview: {
    position: 'relative',
    marginRight: 8,
    borderRadius: 8,
    overflow: 'hidden',
  },
  imagePreview: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  documentPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    padding: 8,
    borderRadius: 8,
    maxWidth: 150,
  },
  documentName: {
    fontSize: 12,
    marginLeft: 4,
    flex: 1,
  },
  removeButton: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: 'rgba(0,0,0,0.6)',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  modalButton: {
    marginBottom: 10,
  },
  cancelButton: {
    marginTop: 10,
  },
});

export default ChatInput;
