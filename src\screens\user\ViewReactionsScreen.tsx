import React, { useEffect, useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text, Card, ActivityIndicator } from 'react-native-paper';
import { useAuth, API_BASE_URL } from '../../context/AuthContext';
import axios from 'axios';
import { DrugReaction } from '../../types/drugReaction';
import { theme } from '../../theme';

const ViewReactionsScreen = () => {
    const { user } = useAuth();
    const [userReactions, setUserReactions] = useState<DrugReaction[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'pending':
                return theme.colors.error;
            case 'resolved':
                return theme.colors.secondary;
            default:
                return theme.colors.text;
        }
    };

    useEffect(() => {
        const fetchReactions = async () => {
            console.log('Current user:', user);
            if (!user) {
                setError('User not found');
                setLoading(false);
                return;
            }

            console.log('User ID:', user.id);

            try {
                const response = await axios.get(`${API_BASE_URL}/api/drug-reactions/user/${user.id}`);
                setUserReactions(response.data);
            } catch (err) {
                setError('Error fetching reactions');
                console.error(err);
            } finally {
                setLoading(false);
            }
        };

        fetchReactions();
    }, [user]);

    if (loading) {
        return (
            <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#0000ff" />
            </View>
        );
    }

    return (
        <ScrollView style={styles.container}>
            <Text style={styles.title}>Your Reported Reactions</Text>
            {error && <Text style={styles.errorText}>{error}</Text>}
            {userReactions.length > 0 ? (
                userReactions.map((reaction) => (
                    <Card key={reaction._id} style={styles.card}>

                        <Card.Content>
                            <Text style={styles.drugName}>{reaction.drugName}</Text>
                            <Text style={styles.description}>{reaction.reactionDetails}</Text>
                            <Text style={styles.severity}>Severity: {reaction.severity}</Text>
                            <Text style={[styles.status, { color: getStatusColor(reaction.status) }]}>
                                Status: {reaction.status}
                            </Text>
                            <Text style={styles.date}>
                                Date Reported: {new Date(reaction.dateReported).toLocaleDateString()}
                            </Text>
                        </Card.Content>
                    </Card>
                ))
            ) : (
                <Text style={styles.emptyText}>No reactions reported yet</Text>
            )}
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        padding: 16,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 16,
    },
    card: {
        marginBottom: 16,
    },
    drugName: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    description: {
        fontSize: 14,
        marginVertical: 4,
    },
    severity: {
        fontSize: 14,
        fontStyle: 'italic',
        color: 'gray',
    },
    status: {
        fontSize: 14,
        fontStyle: 'italic',
        fontWeight: 'bold',
        color: 'gray',
    },
    date: {
        fontSize: 12,
        color: 'gray',
    },
    emptyText: {
        textAlign: 'center',
        color: 'gray',
        fontStyle: 'italic',
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    errorText: {
        color: 'red',
        textAlign: 'center',
    },
});

export default ViewReactionsScreen;
;