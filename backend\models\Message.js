const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  senderId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  receiverId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  content: {
    type: String,
    required: function() { return !this.attachment; } // Required only if no attachment
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  read: {
    type: Boolean,
    default: false
  },
  attachment: {
    type: Object,
    required: false,
    fileType: {
      type: String,
      enum: ['image', 'document', 'audio', 'video', 'other'],
      required: function() { return this.attachment; }
    },
    fileName: {
      type: String,
      required: function() { return this.attachment; }
    },
    fileSize: {
      type: Number,
      required: function() { return this.attachment; }
    },
    fileData: {
      type: String, // Base64 encoded data
      required: function() { return this.attachment; }
    },
    mimeType: {
      type: String,
      required: function() { return this.attachment; }
    }
  }
});

// Index for faster queries
messageSchema.index({ senderId: 1, receiverId: 1 });
messageSchema.index({ timestamp: -1 });

const Message = mongoose.model('Message', messageSchema);

module.exports = Message;