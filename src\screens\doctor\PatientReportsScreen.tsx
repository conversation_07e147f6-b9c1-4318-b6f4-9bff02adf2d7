import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, FlatList } from 'react-native';
import { Text, Card, Button, Searchbar, Chip, IconButton, Surface, Menu } from 'react-native-paper';
import { theme, styles as globalStyles } from '../../theme';
import { useAuth, API_BASE_URL } from '../../context/AuthContext';
import axios from 'axios';
import { DrugReaction } from '../../types/drugReaction';

export const PatientReportsScreen = ({ navigation }: any) => {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [reports, setReports] = useState<DrugReaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filter, setFilter] = useState<'all' | 'pending' | 'resolved'>('all');

  useEffect(() => {
    fetchReports();
  }, []);

  const fetchReports = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/api/drug-reactions`);
      setReports(response.data);
    } catch (err) {
      setError('Failed to fetch reports');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'mild':
        return theme.colors.primary;
      case 'moderate':
        return '#FFA500';
      case 'severe':
        return theme.colors.error;
      default:
        return theme.colors.text;
    }
  };

  const filteredReports = reports
    .filter(report => {
      if (filter === 'pending') return report.status === 'pending';
      if (filter === 'resolved') return report.status === 'resolved';
      return true;
    })
    .filter(report => {
      const patientName = `${report.user?.firstName} ${report.user?.lastName}`.toLowerCase();
      return patientName.includes(searchQuery.toLowerCase()) ||
             report.drugName.toLowerCase().includes(searchQuery.toLowerCase());
    });

  return (
    <View style={styles.container}>
      <Surface style={styles.header}>
        <IconButton
          icon="arrow-left"
          size={24}
          onPress={() => navigation.goBack()}
        />
        <Text style={styles.title}>Patient Details</Text>
      </Surface>

      <View style={styles.filterContainer}>
        <Searchbar
          placeholder="Search by patient or drug name"
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
        />
        <View style={styles.filterChips}>
          <Chip
            selected={filter === 'all'}
            onPress={() => setFilter('all')}
            style={styles.chip}
          >
            All
          </Chip>
          <Chip
            selected={filter === 'pending'}
            onPress={() => setFilter('pending')}
            style={styles.chip}
          >
            Pending
          </Chip>
          <Chip
            selected={filter === 'resolved'}
            onPress={() => setFilter('resolved')}
            style={styles.chip}
          >
            Resolved
          </Chip>
        </View>
      </View>

      <FlatList
        data={filteredReports}
        keyExtractor={(item) => item._id}
        contentContainerStyle={styles.list}
        renderItem={({ item: report }) => (
          <Card style={styles.card}>
            <Card.Content>
              <View style={styles.cardHeader}>
                <View>
                  <Text style={styles.patientName}>
                    {report.user ? `${report.user.firstName} ${report.user.lastName}` : 'Unknown Patient'}
                  </Text>
                  <Text style={styles.drugName}>{report.drugName}</Text>
                </View>
                <Chip
                  mode="outlined"
                  textStyle={{ color: getSeverityColor(report.severity) }}
                >
                  {report.severity}
                </Chip>
              </View>
              
              <Text style={styles.description} numberOfLines={2}>
                {report.reactionDetails}
              </Text>

              <View style={styles.cardFooter}>
                <Text style={styles.date}>
                  Reported: {new Date(report.dateReported).toLocaleDateString()}
                </Text>
                <Button
                  mode="contained"
                  onPress={() => navigation.navigate('PatientProfile', { 
                    patient: report.user,
                    activeTab: 'reports'
                  })}
                >
                  View Details
                </Button>
              </View>
            </Card.Content>
          </Card>
        )}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.sm,
    elevation: 4,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginLeft: theme.spacing.sm,
  },
  filterContainer: {
    padding: theme.spacing.sm,
  },
  searchBar: {
    marginBottom: theme.spacing.sm,
    elevation: 0,
  },
  filterChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  chip: {
    marginRight: theme.spacing.xs,
  },
  list: {
    padding: theme.spacing.sm,
  },
  card: {
    marginBottom: theme.spacing.sm,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.sm,
  },
  patientName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  drugName: {
    fontSize: 14,
    color: theme.colors.primary,
  },
  description: {
    fontSize: 14,
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  date: {
    fontSize: 12,
    color: theme.colors.placeholder,
  },
});