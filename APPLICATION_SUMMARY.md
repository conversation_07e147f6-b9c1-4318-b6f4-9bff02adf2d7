# Application Summary

## Frontend
- **Technology Stack**: 
  - Built using **React** and **TypeScript**.
  - Utilizes **Expo** for cross-platform development (Android, iOS, and web).
  
- **Key Files**:
  - `App.tsx`: Main application component.
  - `src/components/`: Contains reusable components.
  - `src/screens/`: Contains different screens for user interactions.
  - `src/context/`: Manages application state using React Context API.
  - `src/navigation/`: Handles navigation between screens.

- **Start Scripts**:
  - `start`: `expo start` - Starts the Expo development server.
  - `android`: `expo start --android` - Opens the app on an Android device.
  - `ios`: `expo start --ios` - Opens the app on an iOS device.
  - `web`: `expo start --web` - Opens the app in a web browser.

## Backend
- **Technology Stack**:
  - Built using **Node.js** and **Express**.
  - Uses **MongoDB** for data storage.
  - Implements user authentication with **JWT** and **bcrypt**.

- **Key Files**:
  - `index.js`: Main entry point for the backend application.
  - `routes/`: Contains various route files for handling API requests:
    - `staff.js`: Manages staff-related operations (registration, fetching doctors and counsellors).
    - `patients.js`: Fetches patient data.
    - `drugCounselling.js`: Handles drug counselling records.
    - `drugReactions.js`: Manages drug reaction reports and statistics.
    - `chat.js`: Handles chat message sending and retrieval.

- **Key Features**:
  - User registration and login with validation.
  - CRUD operations for staff, patients, drug reactions, and counselling.
  - Real-time chat functionality using **Socket.IO**.

## File Structure
```
<!-- Adverse-Drug-Reaction-Reporting -->
.gitignore
app.json
App.tsx
deployment.md
index.ts
package-lock.json
package.json
README.md
tsconfig.json
assets/
    ├── adaptive-icon.png
    ├── favicon.png
    ├── icon.png
    └── splash-icon.png
backend/
    ├── .env
    ├── createAdmin.js
    ├── index.js
    ├── package-lock.json
    ├── package.json
    ├── middleware/
    │   └── auth.js
    ├── models/
    │   ├── DrugCounselling.js
    │   ├── DrugReaction.js
    │   ├── Message.js
    │   └── User.js
    └── routes/
        ├── chat.js
        ├── drugCounselling.js
        ├── drugReactions.js
        ├── patients.js
        └── staff.js
src/
    ├── components/
    │   ├── ChatInput.tsx
    │   ├── ChatMessage.tsx
    │   ├── CounsellingDataTable.css
    │   ├── CounsellingDataTable.tsx
    │   ├── DrugCounsellingForm.css
    │   └── DrugCounsellingForm.tsx
    ├── context/
    │   ├── AuthContext.tsx
    │   ├── DrugCounsellingContext.tsx
    │   ├── DrugReactionContext.tsx
    │   └── PatientContext.tsx
    ├── navigation/
    │   ├── index.tsx
    │   └── MainNavigator.tsx
    └── screens/
        ├── admin/
        │   ├── AdminDashboard.tsx
        │   └── PatientManagement.tsx
        ├── auth/
        │   ├── LoginScreen.tsx
        │   └── RegisterScreen.tsx
        ├── common/
        │   ├── PatientListScreen.tsx
        │   └── PatientProfileScreen.tsx
        ├── counsellor/
        │   ├── CounsellorChatScreen.tsx
        │   ├── CounsellorDashboard.tsx
        │   ├── DrugCounsellingScreen.css
        │   └── DrugCounsellingScreen.tsx
        ├── doctor/
        │   └── DoctorDashboard.tsx
        └── user/
            ├── PatientChatScreen.tsx
            ├── ReportReactionScreen.tsx
            ├── UserDashboard.tsx
            └── ViewReactionsScreen.tsx
    ├── theme/
    │   └── index.ts
    └── types/
        ├── chat.ts
        ├── counsellingData.ts
        ├── drugReaction.ts
        ├── index.ts
        └── patient.ts
