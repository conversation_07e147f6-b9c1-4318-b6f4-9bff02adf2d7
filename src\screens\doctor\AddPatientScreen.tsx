import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text, TextInput, Button, Surface, IconButton, SegmentedButtons } from 'react-native-paper';
import { DatePickerModal } from 'react-native-paper-dates';
import { theme, styles as globalStyles } from '../../theme';
import { useAuth, API_BASE_URL } from '../../context/AuthContext';
import axios from 'axios';

export const AddPatientScreen = ({ navigation }: any) => {
  const { user } = useAuth();
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [formError, setFormError] = useState('');
  const [loading, setLoading] = useState(false);

  const [formData, setFormData] = useState({
    hospitalNo: '',
    firstName: '',
    lastName: '',
    dateOfBirth: new Date(),
    age: '',
    sex: 'Male',
    weight: '',
    contactNumber: '',
    ethnicity: '',
    pregnant: 'Not Applicable',
    priorCounsel: 'No',
    email: '',
  });

  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      const birthDate = new Date(date);
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const m = today.getMonth() - birthDate.getMonth();
      if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
      setFormData({ ...formData, dateOfBirth: date, age: age.toString() });
    }
    setShowDatePicker(false);
  };

  const validateForm = () => {
    if (!formData.hospitalNo.trim()) return 'Hospital number is required';
    if (!formData.firstName.trim()) return 'First name is required';
    if (!formData.lastName.trim()) return 'Last name is required';
    if (!formData.email.trim()) return 'Email is required';
    if (!formData.contactNumber.trim()) return 'Contact number is required';
    return '';
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const validationError = validateForm();
      if (validationError) {
        setFormError(validationError);
        return;
      }

      const response = await axios.post(`${API_BASE_URL}/api/patients`, {
        ...formData,
        assignedDoctor: user?.id,
        role: 'patient'
      });

      if (response.status === 201) {
        navigation.navigate('PatientList');
      }
    } catch (error) {
      setFormError('Failed to create patient. Please try again.');
      console.error('Error creating patient:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Surface style={styles.header}>
        <IconButton
          icon="arrow-left"
          size={24}
          onPress={() => navigation.goBack()}
        />
        <Text style={styles.title}>Add New Patient</Text>
      </Surface>

      <ScrollView style={styles.content}>
        {formError ? <Text style={styles.error}>{formError}</Text> : null}

        <TextInput
          label="Hospital No"
          value={formData.hospitalNo}
          onChangeText={(text) => setFormData({ ...formData, hospitalNo: text })}
          style={styles.input}
          mode="outlined"
        />

        <TextInput
          label="First Name"
          value={formData.firstName}
          onChangeText={(text) => setFormData({ ...formData, firstName: text })}
          style={styles.input}
          mode="outlined"
        />

        <TextInput
          label="Last Name"
          value={formData.lastName}
          onChangeText={(text) => setFormData({ ...formData, lastName: text })}
          style={styles.input}
          mode="outlined"
        />

        <TextInput
          label="Email"
          value={formData.email}
          onChangeText={(text) => setFormData({ ...formData, email: text })}
          style={styles.input}
          mode="outlined"
          keyboardType="email-address"
        />

        <TextInput
          label="Date of Birth"
          value={formData.dateOfBirth.toLocaleDateString()}
          onFocus={() => setShowDatePicker(true)}
          style={styles.input}
          mode="outlined"
          right={<TextInput.Icon icon="calendar" />}
        />

        <TextInput
          label="Age"
          value={formData.age}
          style={styles.input}
          mode="outlined"
          disabled
        />

        <SegmentedButtons
          value={formData.sex}
          onValueChange={(value) => setFormData({ ...formData, sex: value })}
          buttons={[
            { value: 'Male', label: 'Male' },
            { value: 'Female', label: 'Female' },
            { value: 'Other', label: 'Other' },
          ]}
          style={styles.segmentedButton}
        />

        <TextInput
          label="Weight (kg)"
          value={formData.weight}
          onChangeText={(text) => setFormData({ ...formData, weight: text })}
          style={styles.input}
          mode="outlined"
          keyboardType="numeric"
        />

        <TextInput
          label="Contact Number"
          value={formData.contactNumber}
          onChangeText={(text) => setFormData({ ...formData, contactNumber: text })}
          style={styles.input}
          mode="outlined"
          keyboardType="phone-pad"
        />

        <TextInput
          label="Ethnicity"
          value={formData.ethnicity}
          onChangeText={(text) => setFormData({ ...formData, ethnicity: text })}
          style={styles.input}
          mode="outlined"
        />

        <SegmentedButtons
          value={formData.priorCounsel}
          onValueChange={(value) => setFormData({ ...formData, priorCounsel: value })}
          buttons={[
            { value: 'Yes', label: 'Prior Counselling: Yes' },
            { value: 'No', label: 'Prior Counselling: No' },
          ]}
          style={styles.segmentedButton}
        />

        {formData.sex === 'Female' && (
          <SegmentedButtons
            value={formData.pregnant}
            onValueChange={(value) => setFormData({ ...formData, pregnant: value })}
            buttons={[
              { value: 'Yes', label: 'Pregnant: Yes' },
              { value: 'No', label: 'Pregnant: No' },
              { value: 'Not Applicable', label: 'N/A' },
            ]}
            style={styles.segmentedButton}
          />
        )}

        <Button
          mode="contained"
          onPress={handleSubmit}
          style={styles.submitButton}
          loading={loading}
        >
          Add Patient
        </Button>
      </ScrollView>

      <DatePickerModal
        visible={showDatePicker}
        onDismiss={() => setShowDatePicker(false)}
        date={formData.dateOfBirth}
        onConfirm={({ date }) => handleDateChange(date)}
        validRange={{
          startDate: new Date(1900, 0, 1),
          endDate: new Date(),
        }}
        mode="single"
        locale="en"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.sm,
    elevation: 4,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginLeft: theme.spacing.sm,
  },
  content: {
    flex: 1,
    padding: theme.spacing.md,
  },
  input: {
    marginBottom: theme.spacing.sm,
  },
  segmentedButton: {
    marginBottom: theme.spacing.md,
  },
  submitButton: {
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.xl,
  },
  error: {
    color: theme.colors.error,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
});