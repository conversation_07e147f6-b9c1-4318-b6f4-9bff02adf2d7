const express = require('express');
const User = require('../models/User');
const bcrypt = require('bcrypt');
const router = new express.Router();
const auth = require('../middleware/auth');
const mongoose = require('mongoose');

// Create new staff member
router.post('/', async (req, res) => {
  const { email, name, role, password, specialization, phoneNumber, designation, department } = req.body;

  if (!email || !name || !role || !password || !phoneNumber || !designation || !department) {
    return res.status(400).send({ error: 'Please provide all required fields.' });
  }

  try {
    // Check if email already exists
    const existingEmail = await User.findOne({ email });
    if (existingEmail) {
      return res.status(400).send({ error: 'Email already registered.' });
    }

    // Generate a unique username from name
    const generateUsername = async (baseName) => {
      // Remove special characters and spaces, convert to lowercase
      let username = baseName.toLowerCase().replace(/[^a-zA-Z0-9]/g, '');
      
      // If username is too short, append role first letter
      if (username.length < 3) {
        username += role.charAt(0);
      }
      
      let finalUsername = username;
      let counter = 1;
      
      // Keep checking until we find a unique username
      while (await User.findOne({ username: finalUsername })) {
        finalUsername = `${username}${counter}`;
        counter++;
      }
      
      return finalUsername;
    };

    const username = await generateUsername(name);
    const hashedPassword = await bcrypt.hash(password, 10);
    
    const user = new User({ 
      email, 
      name, 
      role, 
      password: hashedPassword, 
      specialization,
      phoneNumber,
      designation,
      department,
      username
    });
    
    await user.save();
    res.status(201).send(user);
  } catch (error) {
    console.error('Error details:', error.message);
    console.error('Error stack:', error.stack);
    res.status(400).send({ error: 'Error creating staff member.' });
  }
});

// Get all doctors
router.get('/doctors', async (req, res) => {
  try {
    const doctors = await User.find({ role: 'doctor' })
      .select('name email phoneNumber specialization designation department')
      .sort({ name: 1 });
    res.send(doctors);
  } catch (error) {
    console.error('Error fetching doctors:', error);
    res.status(500).send({ error: 'Error fetching doctors' });
  }
});

// Get single counsellor (for chat)
router.get('/counsellor', async (req, res) => {
  try {
    const counsellor = await User.findOne({ role: 'drug-counsellor' });
    if (!counsellor) {
      return res.status(404).json({ error: 'No drug counsellor found' });
    }
    res.json(counsellor);
  } catch (error) {
    console.error('Error fetching counsellor:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get all counsellors
router.get('/counsellors', async (req, res) => {
  try {
    const counsellors = await User.find({ role: 'drug-counsellor' })
      .select('name email phoneNumber specialization designation department _id')
      .sort({ name: 1 });
    res.status(200).json(counsellors);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});
router.get('/', async (req, res) => {
  try {
    const staff = await User.find({ role: { $in: ['doctor', 'drug-counsellor'] } })
      .select('name email phoneNumber specialization designation department role');
    res.status(200).json(staff);
  } catch (error) {
    console.error('Error fetching staff:', error);
    res.status(500).json({ error: 'Failed to fetch staff' });
  }
});
// Get user counts
router.get('/counts', async (req, res) => {
  try {
    const doctorCount = await User.countDocuments({ role: 'doctor' });
    const counsellorCount = await User.countDocuments({ role: 'drug-counsellor' });
    const patientCount = await User.countDocuments({ role: 'patient' });
    res.send({
      totalDoctors: doctorCount,
      totalCounsellors: counsellorCount,
      totalPatients: patientCount,
    });
  } catch (error) {
    console.error('Error fetching user counts:', error);
    res.status(500).send({ error: 'Error fetching user counts' });
  }
});
// Update staff member by ID
router.patch('/:id', async (req, res) => {
  const { id } = req.params;
  const updates = { ...req.body };

  try {
    // If password is provided, hash it before updating
    if (updates.password) {
      updates.password = await bcrypt.hash(updates.password, 10);
    }

    const user = await User.findByIdAndUpdate(id, updates, { new: true });
    if (!user) {
      return res.status(404).send({ error: 'Staff member not found.' });
    }
    res.send(user);
  } catch (error) {
    console.error('Error updating staff member:', error);
    res.status(400).send({ error: 'Error updating staff member.' });
  }
});

router.delete('/:id', auth, async (req, res) => {
  try {
    console.log('Received delete request for staff ID:', req.params.id);

    const { id } = req.params;

    // Check if the ID is valid
    if (!mongoose.Types.ObjectId.isValid(id)) {
      console.log('Invalid staff ID format:', id);
      return res.status(400).json({ error: 'Invalid staff ID format' });
    }

    // Check if the user is authenticated and authorized
    if (!req.user || !req.user.role) {
      console.log('User not authenticated or role missing');
      return res.status(403).json({ error: 'User not authenticated' });
    }

    if (req.user.role !== 'admin' && req.user.role !== 'drug-counsellor') {
      console.log('Permission denied for role:', req.user.role);
      return res.status(403).json({ error: 'You do not have permission to delete this staff' });
    }

    // Attempt to delete the staff member
    const staff = await User.findByIdAndDelete(id);

    if (!staff) {
      console.log('Staff not found with ID:', id);
      return res.status(404).json({ error: 'Staff not found' });
    }

    console.log('Staff deleted successfully:', staff._id);
    return res.status(200).json({ message: 'Staff deleted successfully' });
  } catch (error) {
    console.error('Error during staff deletion:', error);
    res.status(500).json({ error: error.message || 'Server error while deleting staff' });
  }
});

module.exports = router;