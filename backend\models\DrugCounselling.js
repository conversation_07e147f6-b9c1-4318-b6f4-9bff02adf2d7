const mongoose = require('mongoose');

const DrugCounsellingSchema = new mongoose.Schema({
    date: {
        type: Date,
        required: true
    },
    hospitalNo: {
        type: String,
        required: true
    },
    patientName: {
        type: String,
        required: true
    },
    ageSex: {
        type: String,
        required: true
    },
    address: {
        type: String,
        required: true
    },
    contactNo: {
        type: String,
        required: true
    },
    diagnosis: {
        type: String,
        required: true
    },
    medicalHistory: {
        type: String,
        required: false
    },
    medicationHistory: {
        type: String,
        required: false
    },
    allergyHistory: {
        type: String,
        required: false
    },
    alcoholIntake: {
        type: String,
        required: false
    },
    smokingHistory: {
        type: String,
        required: false
    },
    prescribedMedicine: {
        type: String,
        required: false
    },
    counselingProvided: {
        type: String,
        required: false
    },
    averageCounselingTime: {
        type: Number,
        required: false
    },
    department: {
        type: String,
        required: false
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    createdAt: {
        type: Date,
        default: Date.now,
        required: true
    },
    modifiedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: false
    },
    modifiedAt: {
        type: Date,
        default: Date.now,
        required: false
    }
});

module.exports = mongoose.model('DrugCounselling', DrugCounsellingSchema);
