import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Dimensions } from 'react-native';
import { Text, Button, Card, IconButton, Dialog, Portal, Paragraph, Avatar, useTheme } from 'react-native-paper';
import { theme, styles as globalStyles } from '../../theme';
import { useAuth } from '../../context/AuthContext';

// Simple responsive breakpoint - consistent with other dashboard components
const SCREEN_BREAKPOINT_TABLET = 768;

export const DoctorDashboard = ({ navigation }: any) => {
  const { user, logout } = useAuth();
  const paperTheme = useTheme(); // Add useTheme hook for accessing theme in the component
  const [screenWidth, setScreenWidth] = useState(Dimensions.get('window').width);
  const [logoutDialogVisible, setLogoutDialogVisible] = useState(false);

  // Update screen width when dimensions change
  useEffect(() => {
    const updateLayout = () => {
      setScreenWidth(Dimensions.get('window').width);
    };

    // Add event listener for dimension changes
    const subscription = Dimensions.addEventListener('change', updateLayout);

    // Clean up event listener on component unmount
    return () => subscription.remove();
  }, []);

  // Function to show the logout confirmation dialog
  const showLogoutDialog = () => {
    setLogoutDialogVisible(true);
  };

  // Function to handle logout confirmation
  const handleLogoutConfirm = async () => {
    setLogoutDialogVisible(false);
    await logout();
  };

  // Function to dismiss the logout dialog
  const hideLogoutDialog = () => {
    setLogoutDialogVisible(false);
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <View>
          <Text style={styles.title}>Doctor Dashboard</Text>
          <Text style={styles.subtitle}>Welcome, Dr. {user?.name}</Text>
        </View>
        <IconButton
          icon="logout"
          size={24}
          onPress={showLogoutDialog}
          style={styles.logoutIcon}
        />
      </View>
            <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.cardTitle}>Patient Management</Text>
          <Button
            mode="contained"
            style={styles.button}
            icon="file-document"
            onPress={() => navigation.navigate('PatientReports')}
          >
            Patient Details
          </Button>
        </Card.Content>
      </Card>
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.cardTitle}>Communications</Text>
          <Button
            mode="contained"
            style={styles.button}
            icon="chat"
            onPress={() => navigation.navigate('DoctorChat')}
          >
            Chat with Drug Counsellor
          </Button>
        </Card.Content>
      </Card>

      {/* Logout Confirmation Dialog */}
      <Portal>
        <Dialog
          visible={logoutDialogVisible}
          onDismiss={hideLogoutDialog}
          style={[styles.dialogContainer, { width: screenWidth > 500 ? 400 : '85%', alignSelf: 'center' }]}
        >
          <View style={styles.iconContainer}>
            <Avatar.Icon
              size={screenWidth < SCREEN_BREAKPOINT_TABLET ? 56 : 64}
              icon="logout"
              color="white"
              style={{ backgroundColor: paperTheme.colors.error }}
            />
          </View>
          <Dialog.Title style={[
            styles.dialogTitle,
            screenWidth < SCREEN_BREAKPOINT_TABLET && { fontSize: 18, marginBottom: theme.spacing.xs }
          ]}>
            Confirm Logout
          </Dialog.Title>
          <Dialog.Content style={styles.dialogContent}>
            <Paragraph style={[
              styles.dialogMessage,
              screenWidth < SCREEN_BREAKPOINT_TABLET && { fontSize: 14, marginBottom: theme.spacing.sm }
            ]}>
              Are you sure you want to log out of the Adverse Drug Reaction Reporting System?
            </Paragraph>
          </Dialog.Content>
          <Dialog.Actions style={[
            styles.dialogActions,
            screenWidth < SCREEN_BREAKPOINT_TABLET && {
              paddingHorizontal: theme.spacing.sm,
              paddingBottom: theme.spacing.sm
            }
          ]}>
            <Button
              onPress={hideLogoutDialog}
              style={[
                styles.cancelButton,
                screenWidth < SCREEN_BREAKPOINT_TABLET && {
                  paddingHorizontal: theme.spacing.sm,
                  minWidth: 80
                }
              ]}
              labelStyle={[
                { color: paperTheme.colors.onSurface },
                screenWidth < SCREEN_BREAKPOINT_TABLET && { fontSize: 12 }
              ]}
              mode="outlined"
            >
              Cancel
            </Button>
            <Button
              onPress={handleLogoutConfirm}
              style={[
                styles.logoutButton,
                screenWidth < SCREEN_BREAKPOINT_TABLET && {
                  paddingHorizontal: theme.spacing.sm,
                  minWidth: 80
                }
              ]}
              mode="contained"
              labelStyle={screenWidth < SCREEN_BREAKPOINT_TABLET ? { fontSize: 12, color: 'white' } : { color: 'white' }}
            >
              Log Out
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    ...globalStyles.container,
    padding: theme.spacing.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.lg,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.text,
    marginTop: theme.spacing.xs,
  },
  logoutIcon: {
    margin: 0,
  },
  card: {
    ...globalStyles.card,
    marginVertical: theme.spacing.sm,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: theme.spacing.md,
    color: theme.colors.text,
  },
  button: {
    marginVertical: theme.spacing.xs,
  },
  // Logout dialog styles
  dialogContainer: {
    borderRadius: theme.roundness * 2,
    backgroundColor: theme.colors.surface,
    ...globalStyles.shadow,
  },
  dialogTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.primary,
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
  },
  dialogContent: {
    paddingHorizontal: theme.spacing.lg,
    paddingBottom: theme.spacing.md,
  },
  dialogMessage: {
    fontSize: 16,
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  dialogActions: {
    marginTop: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    paddingBottom: theme.spacing.md,
    justifyContent: 'space-between',
  },
  cancelButton: {
    marginRight: theme.spacing.md,
  },
  logoutButton: {
    backgroundColor: theme.colors.error,
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
});
