import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { TextInput, Button, Text, ActivityIndicator, IconButton } from 'react-native-paper';
import { theme, styles as globalStyles } from '../../theme';
import { useAuth } from '../../context/AuthContext';
import type { RegisterData } from '../../types';
import { Dropdown } from 'react-native-element-dropdown';
import AntDesign from '@expo/vector-icons/AntDesign';
import { DatePickerModal } from 'react-native-paper-dates';

// Options for dropdowns
const sexOptions = [
  { label: 'Male', value: 'Male' },
  { label: 'Female', value: 'Female' },
];
const pregnancyOptions = [
  { label: 'Yes', value: "Yes" },
  { label: 'No', value: "No" },
  { label: 'Not Applicable', value: "Not Applicable" },
];
const yesNoOptions = [
  { label: 'Yes', value: "Yes" },
  { label: 'No', value: "No" },
];
const ethnicityOptions = [
  { label: 'Brahmin/Chhetri', value: 'Brahmin/Chhetri' },
  { label: 'Terai/Madhesi', value: 'Terai/Madhesi' },
  { label: 'Dalits', value: 'Dalits' },
  { label: 'Newar', value: 'Newar' },
  { label: 'Janajati', value: 'Janajati' },
  { label: 'Muslim', value: 'Muslim' },
  { label: 'Other', value: 'Other' },
];

// Helper function to calculate age from the birth date.
const calculateAge = (birthDate: Date): number => {
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const m = today.getMonth() - birthDate.getMonth();
  if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
};

export const RegisterScreen = ({ navigation }: any) => {
  const { register, isLoading, error } = useAuth();
  const [formData, setFormData] = useState<RegisterData>({
    email: '',
    password: '',
    username: '',
    hospitalNo: '',
    firstName: '',
    lastName: '',
    age: 0,
    sex: '',
    weight: 0,
    contactNumber: '',
    ethnicity: '',
    pregnant: '',
    dateOfBirth: '',
    priorCounsel: '',
  });
  const [confirmPassword, setConfirmPassword] = useState('');
  const [formError, setFormError] = useState<string>('');
  const [isFocus, setIsFocus] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false); // Date picker visibility state
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);

  const handleRegister = async () => {
    console.log('Current formData:', formData);
    try {
      setFormError('');

      // Validate all fields
      if (
        !formData.email ||
        !formData.password ||
        !formData.username ||
        !confirmPassword ||
        !formData.hospitalNo ||
        !formData.firstName ||
        !formData.lastName ||
        !formData.age.toString() ||
        !formData.sex ||
        !formData.weight.toString() ||
        !formData.contactNumber ||
        !formData.ethnicity ||
        !formData.dateOfBirth ||
        !formData.priorCounsel
      ) {
        throw new Error('Please fill in all fields');
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        throw new Error('Please enter a valid email address');
      }

      // Validate username format
      const usernameRegex = /^[a-zA-Z0-9_]+$/;
      if (!usernameRegex.test(formData.username)) {
        throw new Error('Username can only contain letters, numbers, and underscores');
      }

      // Validate password
      if (formData.password.length < 6) {
        throw new Error('Password must be at least 6 characters long');
      }

      // Confirm password
      if (formData.password !== confirmPassword) {
        throw new Error('Passwords do not match');
      }

      // Set role to patient explicitly
      const registrationData = {
        ...formData,
        role: 'patient' as 'patient',
        age: Number(formData.age),
        weight: Number(formData.weight),
      };

      try {
        // Wait for registration to complete and get the registered user
        const registeredUser = await register(registrationData);

        if (registeredUser && registeredUser.role === 'patient') {
          navigation.navigate('Login');
        }
      } catch (error: any) {
        // Check for specific error messages from the server
        if (error.response?.data?.error) {
          if (error.response.data.error.includes('Email already registered')) {
            setFormError('This email address is already registered.');
          } else if (error.response.data.error.includes('Username already taken')) {
            setFormError('This username is already taken.');
          } else if (error.response.data.error.includes('Hospital number already taken')) {
            setFormError('This Hospital number is already taken.');
          } else {
            setFormError(error.response.data.error);
          }
        } else {
          setFormError(error instanceof Error ? error.message : 'Registration failed');
        }
      }
    } catch (err) {
      setFormError(err instanceof Error ? err.message : 'Registration failed');
    }
  };

  const handleDateChange = (date: Date | undefined) => {
    console.log("Selected Date of Birth:", date);
    if (date) {
      // Convert date to ISO date string (YYYY-MM-DD)
      const isoDate = new Date(date.getTime() - date.getTimezoneOffset() * 60000)
        .toISOString()
        .split('T')[0];
      // Calculate age from the selected date
      const age = calculateAge(date);
      setFormData({ ...formData, dateOfBirth: isoDate, age });
    }
    setShowDatePicker(false);
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  return (
    <ScrollView contentContainerStyle={styles.scrollContainer} keyboardShouldPersistTaps="handled">
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Patient Registration</Text>
          <Text style={styles.subtitle}>Create your patient account</Text>
        </View>

        {formError && (
          <View style={styles.errorContainer}>
            <Text style={styles.error}>{formError}</Text>
          </View>
        )}

        <TextInput
          label="Hospital No"
          value={formData.hospitalNo}
          onChangeText={(text) => setFormData({ ...formData, hospitalNo: text })}
          style={styles.input}
          mode="outlined"
          activeOutlineColor={theme.colors.primary}
        />

        <TextInput
          label="First Name"
          value={formData.firstName}
          onChangeText={(text) => setFormData({ ...formData, firstName: text })}
          style={styles.input}
          mode="outlined"
          activeOutlineColor={theme.colors.primary}
        />

        <TextInput
          label="Last Name"
          value={formData.lastName}
          onChangeText={(text) => setFormData({ ...formData, lastName: text })}
          style={styles.input}
          mode="outlined"
          activeOutlineColor={theme.colors.primary}
        />

        {/* Date of Birth Picker */}
        <TextInput
          label="Date of Birth"
          value={
            formData.dateOfBirth
              ? new Date(formData.dateOfBirth).toLocaleDateString('en-US')
              : ''
          }
          onFocus={() => setShowDatePicker(true)}
          style={styles.input}
          mode="outlined"
          activeOutlineColor={theme.colors.primary}
          left={<TextInput.Icon icon="calendar" color={theme.colors.primary} />}
        />
        <DatePickerModal
          mode="single"
          visible={showDatePicker}
          onDismiss={() => setShowDatePicker(false)}
          date={formData.dateOfBirth ? new Date(formData.dateOfBirth) : new Date()}
          onConfirm={({ date }) => handleDateChange(date)}
          locale="en"
        />

        <TextInput
          label="Age"
          value={formData.age.toString()}
          onChangeText={(text) => setFormData({ ...formData, age: Number(text) })}
          style={styles.input}
          mode="outlined"
          editable={false}  // Disables direct editing
          activeOutlineColor={theme.colors.primary}
        />


        <Dropdown
          style={[styles.dropdown, isFocus && { borderColor: 'blue' }]}
          placeholderStyle={styles.placeholderStyle}
          selectedTextStyle={styles.selectedTextStyle}
          data={sexOptions}
          labelField="label"
          valueField="value"
          placeholder="Select Sex"
          value={formData.sex}
          onFocus={() => setIsFocus(true)}
          onBlur={() => setIsFocus(false)}
          onChange={(item) => {
            setFormData({ ...formData, sex: item.value });
            setIsFocus(false);
          }}
          renderLeftIcon={() => (
            <AntDesign style={styles.icon} color={isFocus ? 'blue' : 'black'} name="Safety" size={20} />
          )}
        />

        <TextInput
          label="Weight (Kg)"
          value={formData.weight.toString()}
          onChangeText={(text) => setFormData({ ...formData, weight: Number(text) })}
          style={styles.input}
          mode="outlined"
          keyboardType="numeric"
          activeOutlineColor={theme.colors.primary}
        />

        <TextInput
          label="Contact Number"
          value={formData.contactNumber}
          onChangeText={(text) => setFormData({ ...formData, contactNumber: text })}
          style={styles.input}
          mode="outlined"
          keyboardType="phone-pad"
          activeOutlineColor={theme.colors.primary}
        />

        <Dropdown
          style={[styles.dropdown, isFocus && { borderColor: 'blue' }]}
          placeholderStyle={styles.placeholderStyle}
          selectedTextStyle={styles.selectedTextStyle}
          data={ethnicityOptions}
          labelField="label"
          valueField="value"
          placeholder="Select Ethnicity"
          value={formData.ethnicity}
          onFocus={() => setIsFocus(true)}
          onBlur={() => setIsFocus(false)}
          onChange={(item) => {
            setFormData({ ...formData, ethnicity: item.value });
            setIsFocus(false);
          }}
          renderLeftIcon={() => (
            <AntDesign style={styles.icon} color={isFocus ? 'blue' : 'black'} name="Safety" size={20} />
          )}
        />

        {formData.age >= 18 && formData.sex === 'Female' && (
          <Dropdown
            style={[styles.dropdown, isFocus && { borderColor: 'blue' }]}
            placeholderStyle={styles.placeholderStyle}
            selectedTextStyle={styles.selectedTextStyle}
            data={pregnancyOptions}
            labelField="label"
            valueField="value"
            placeholder="Pregnant?"
            value={formData.pregnant}
            onFocus={() => setIsFocus(true)}
            onBlur={() => setIsFocus(false)}
            onChange={(item) => {
              setFormData({ ...formData, pregnant: item.value });
              setIsFocus(false);
            }}
            renderLeftIcon={() => (
              <AntDesign style={styles.icon} color={isFocus ? 'blue' : 'black'} name="Safety" size={20} />
            )}
          />
        )}

        <Dropdown
          style={[styles.dropdown, isFocus && { borderColor: 'blue' }]}
          placeholderStyle={styles.placeholderStyle}
          selectedTextStyle={styles.selectedTextStyle}
          data={yesNoOptions}
          labelField="label"
          valueField="value"
          placeholder="Prior Counsel of Medication?"
          value={formData.priorCounsel}
          onFocus={() => setIsFocus(true)}
          onBlur={() => setIsFocus(false)}
          onChange={(item) => {
            setFormData({ ...formData, priorCounsel: item.value });
            setIsFocus(false);
          }}
          renderLeftIcon={() => (
            <AntDesign style={styles.icon} color={isFocus ? 'blue' : 'black'} name="Safety" size={20} />
          )}
        />

        <TextInput
          label="Username"
          value={formData.username}
          onChangeText={(text) => setFormData({ ...formData, username: text })}
          style={styles.input}
          mode="outlined"
          activeOutlineColor={theme.colors.primary}
        />

        <TextInput
          label="Email Address"
          value={formData.email}
          onChangeText={(text) => setFormData({ ...formData, email: text })}
          style={styles.input}
          mode="outlined"
          keyboardType="email-address"
          autoCapitalize="none"
          activeOutlineColor={theme.colors.primary}
        />

        <TextInput
          label="Password"
          value={formData.password}
          onChangeText={(text) => setFormData({ ...formData, password: text })}
          style={styles.input}
          mode="outlined"
          secureTextEntry={!passwordVisible}
          activeOutlineColor={theme.colors.primary}
          right={
            <TextInput.Icon
              icon={passwordVisible ? "eye-off" : "eye"}
              color={theme.colors.primary}
              onPress={() => setPasswordVisible(!passwordVisible)}
            />
          }
        />

        <TextInput
          label="Confirm Password"
          value={confirmPassword}
          onChangeText={setConfirmPassword}
          style={styles.input}
          mode="outlined"
          secureTextEntry={!confirmPasswordVisible}
          activeOutlineColor={theme.colors.primary}
          right={
            <TextInput.Icon
              icon={confirmPasswordVisible ? "eye-off" : "eye"}
              color={theme.colors.primary}
              onPress={() => setConfirmPasswordVisible(!confirmPasswordVisible)}
            />
          }
        />

        <Button
          mode="contained"
          onPress={handleRegister}
          style={styles.button}
          buttonColor={theme.colors.primary}
        >
          Register
        </Button>

        <Button
          mode="text"
          onPress={() => navigation.navigate('Login')}
          style={styles.linkButton}
          textColor={theme.colors.primary}
        >
          Already have an account? Login
        </Button>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
  },
  container: {
    ...globalStyles.container,
    justifyContent: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  header: {
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.text,
    marginTop: theme.spacing.xs,
  },
  input: {
    ...globalStyles.input,
    backgroundColor: theme.colors.surface,
  },
  dropdown: {
    height: 50,
    borderColor: 'gray',
    borderWidth: 0.5,
    borderRadius: 8,
    paddingHorizontal: 8,
    marginBottom: 16,
  },
  icon: {
    marginRight: 5,
  },
  placeholderStyle: {
    fontSize: 16,
  },
  selectedTextStyle: {
    fontSize: 16,
  },
  button: {
    marginVertical: 10,
    padding: 10,
    backgroundColor: theme.colors.primary,
  },
  linkButton: {
    marginVertical: 10,
    padding: 10,
    color: theme.colors.primary,
  },
  errorContainer: {
    backgroundColor: '#ffebee',
    padding: 10,
    borderRadius: 5,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#ffcdd2',
  },
  error: {
    color: '#d32f2f',
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default RegisterScreen;
