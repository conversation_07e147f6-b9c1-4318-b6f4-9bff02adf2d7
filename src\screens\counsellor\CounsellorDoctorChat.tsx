import React, { useState, useEffect, useCallback, useRef } from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity, Dimensions, TextInput } from 'react-native';
import { Text, IconButton } from 'react-native-paper';
import { theme } from '../../theme';
import { useAuth, API_BASE_URL } from '../../context/AuthContext';
import axios from 'axios';
import { io } from 'socket.io-client';
import ChatMessage from '../../components/ChatMessage';
import ChatInput from '../../components/ChatInput';
import { Message } from '../../types/chat';

export const CounsellorDoctorChat = () => {
    const { user } = useAuth();
    const [messages, setMessages] = useState<Message[]>([]);
    const [loading, setLoading] = useState(true);
    const [doctors, setDoctors] = useState<any[]>([]);
    const [selectedDoctor, setSelectedDoctor] = useState<string | null>(null);
    const [socket, setSocket] = useState<any>(null);
    const [doctorSearchTerm, setDoctorSearchTerm] = useState('');
    const [unreadMessages, setUnreadMessages] = useState<{ [key: string]: number }>({});
    const [isSearchExpanded, setIsSearchExpanded] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [isUserAtBottom, setIsUserAtBottom] = useState(true);
    const flatListRef = useRef<FlatList>(null);

    const screenWidth = Dimensions.get('window').width;
    const isMobile = screenWidth < 600;

    // Initialize socket and fetch doctors
    useEffect(() => {
        const newSocket = io(API_BASE_URL);
        setSocket(newSocket);

        const fetchDoctors = async () => {
            try {
                const response = await axios.get(`${API_BASE_URL}/api/staff/doctors`);
                setDoctors(response.data);
            } catch (error) {
                console.error('Error fetching doctors:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchDoctors();

        return () => {
            if (newSocket) {
                newSocket.disconnect();
            }
        };
    }, []);

    // Socket message handling
    useEffect(() => {
        if (!socket || !user?.id) return;

        socket.emit('join-room', user.id);

        socket.on('receive-message', (message: any) => {
            setMessages((prev) => {
                if (!prev.some((m) => m.id === message.id)) {
                    return [...prev, message];
                }
                return prev;
            });

            if (message.receiverId === user.id && message.senderId !== selectedDoctor) {
            setUnreadMessages((prev) => {
                const count = prev[message.senderId] || 0;
                return {
                ...prev,
                [message.senderId]: count + 1,
                };
            });
            }


            if (isUserAtBottom) {
                setTimeout(() => {
                    flatListRef.current?.scrollToEnd({ animated: true });
                }, 100);
            }
        });

        return () => {
            socket.off('receive-message');
        };
    }, [socket, user, isUserAtBottom]);

    // Fetch chat history when doctor is selected
    useEffect(() => {
        if (!selectedDoctor || !user?.id) return;

        const fetchChatHistory = async () => {
            try {
                const response = await axios.get(
                    `${API_BASE_URL}/api/chat/history/${user.id}/${selectedDoctor}`
                );

                const formatted = response.data.map((msg: any) => ({
                    id: msg._id,
                    text: msg.content,
                    timestamp: msg.timestamp,
                    senderId: msg.senderId._id,
                    senderName: msg.senderId.name,
                }));

                setMessages(formatted.reverse());
                setUnreadMessages((prev) => ({
                    ...prev,
                    [selectedDoctor]: 0,
                }));

                setTimeout(() => {
                    flatListRef.current?.scrollToEnd({ animated: false });
                }, 100);
            } catch (error) {
                console.error('Error fetching chat history:', error);
            }
        };

        fetchChatHistory();
    }, [selectedDoctor, user]);

    const handleScroll = (event: any) => {
        const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
        const atBottom = layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;
        setIsUserAtBottom(atBottom);
    };

    const sendMessage = async (text: string) => {
        if (!selectedDoctor || !user?.id || !socket) return;

        try {
            const response = await axios.post(`${API_BASE_URL}/api/chat/send`, {
                sender: user.id,
                receiver: selectedDoctor,
                content: text,
                senderRole: 'counsellor'
            });

            socket.emit('send-message', {
                id: response.data._id,
                text: text,
                timestamp: new Date().toISOString(),
                senderId: user.id,
                senderName: user.name
            });
        } catch (error) {
            console.error('Error sending message:', error);
        }
    };

    const filteredDoctors = doctors.filter((d) =>
        d.name.toLowerCase().includes(doctorSearchTerm.toLowerCase())
    );

    const displayedMessages = messages.filter((msg) =>
        msg.text.toLowerCase().includes(searchTerm.toLowerCase())
    );

    return (
        <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
            {isMobile ? (
                selectedDoctor ? (
                    <View style={styles.fullWidthChatContainer}>
                        <View style={styles.chatHeader}>
                            <IconButton
                                icon="arrow-left"
                                onPress={() => setSelectedDoctor(null)}
                                size={24}
                            />
                            <Text style={styles.selectedDoctorName}>
                                {doctors.find((d) => d._id === selectedDoctor)?.name || 'Doctor'}
                            </Text>
                            <View style={styles.searchContainer}>
                                {isSearchExpanded ? (
                                    <View style={styles.searchInputContainer}>
                                        <TextInput
                                            style={styles.searchInput}
                                            placeholder="Search messages..."
                                            value={searchTerm}
                                            onChangeText={setSearchTerm}
                                            autoFocus
                                        />
                                        <IconButton
                                            icon="close"
                                            onPress={() => {
                                                setSearchTerm('');
                                                setIsSearchExpanded(false);
                                            }}
                                            size={20}
                                        />
                                    </View>
                                ) : (
                                    <IconButton
                                        icon="magnify"
                                        onPress={() => setIsSearchExpanded(true)}
                                        size={24}
                                    />
                                )}
                            </View>
                        </View>

                        <FlatList
                            ref={flatListRef}
                            data={displayedMessages}
                            keyExtractor={(item) => item.id}
                            renderItem={({ item }) => (
                                <ChatMessage
                                    message={item.text}
                                    isCurrentUser={item.senderId === user?.id}
                                    timestamp={item.timestamp}
                                    senderName={item.senderName}
                                />
                            )}
                            contentContainerStyle={styles.messagesContainer}
                            onScroll={handleScroll}
                        />
                        <ChatInput onSend={sendMessage} />
                    </View>
                ) : (
                    <View style={styles.fullWidthDoctorList}>
                        <View style={styles.doctorSearchContainer}>
                            <TextInput
                                style={styles.doctorSearchInput}
                                placeholder="Search doctors..."
                                value={doctorSearchTerm}
                                onChangeText={setDoctorSearchTerm}
                            />
                            {doctorSearchTerm.length > 0 && (
                                <Text style={styles.doctorCount}>
                                    {filteredDoctors.length} doctors found
                                </Text>
                            )}
                        </View>

                        <FlatList
                            data={filteredDoctors}
                            keyExtractor={(item) => item._id}
                            renderItem={({ item: doctor }) => (
                                <TouchableOpacity
                                    style={[
                                        styles.doctorItem,
                                        selectedDoctor === doctor._id && styles.selectedDoctor
                                    ]}
                                    onPress={() => setSelectedDoctor(doctor._id)}
                                >
                                    <Text style={styles.doctorName}>{doctor.name}</Text>
                                    {unreadMessages[doctor._id] > 0 && (
                                        <View style={styles.badge}>
                                            <Text style={styles.badgeText}>
                                                {unreadMessages[doctor._id]}
                                            </Text>
                                        </View>
                                    )}
                                </TouchableOpacity>
                            )}
                            contentContainerStyle={styles.doctorList}
                        />
                    </View>
                )
            ) : (
                <>
                    <View style={styles.leftPanel}>
                        <View style={styles.doctorSearchContainer}>
                            <TextInput
                                style={styles.doctorSearchInput}
                                placeholder="Search doctors..."
                                value={doctorSearchTerm}
                                onChangeText={setDoctorSearchTerm}
                            />
                            {doctorSearchTerm.length > 0 && (
                                <Text style={styles.doctorCount}>
                                    {filteredDoctors.length} doctors found
                                </Text>
                            )}
                        </View>

                        <FlatList
                            data={filteredDoctors}
                            keyExtractor={(item) => item._id}
                            renderItem={({ item: doctor }) => (
                                <TouchableOpacity
                                    style={[
                                        styles.doctorItem,
                                        selectedDoctor === doctor._id && styles.selectedDoctor
                                    ]}
                                    onPress={() => setSelectedDoctor(doctor._id)}
                                >
                                    <Text style={styles.doctorName}>{doctor.name}</Text>
                                    {unreadMessages[doctor._id] > 0 && (
                                        <View style={styles.badge}>
                                            <Text style={styles.badgeText}>
                                                {unreadMessages[doctor._id]}
                                            </Text>
                                        </View>
                                    )}
                                </TouchableOpacity>
                            )}
                            contentContainerStyle={styles.doctorList}
                        />
                    </View>

                    <View style={styles.rightPanel}>
                        {selectedDoctor ? (
                            <>
                                <View style={styles.chatHeader}>
                                    <Text style={styles.selectedDoctorName}>
                                        {doctors.find((d) => d._id === selectedDoctor)?.name || 'Doctor'}
                                    </Text>
                                    <View style={styles.searchContainer}>
                                        {isSearchExpanded ? (
                                            <View style={styles.searchInputContainer}>
                                                <TextInput
                                                    style={styles.searchInput}
                                                    placeholder="Search messages..."
                                                    value={searchTerm}
                                                    onChangeText={setSearchTerm}
                                                    autoFocus
                                                />
                                                <IconButton
                                                    icon="close"
                                                    onPress={() => {
                                                        setSearchTerm('');
                                                        setIsSearchExpanded(false);
                                                    }}
                                                    size={20}
                                                />
                                            </View>
                                        ) : (
                                            <IconButton
                                                icon="magnify"
                                                onPress={() => setIsSearchExpanded(true)}
                                                size={24}
                                            />
                                        )}
                                    </View>
                                </View>

                                <FlatList
                                    ref={flatListRef}
                                    data={displayedMessages}
                                    keyExtractor={(item) => item.id}
                                    renderItem={({ item }) => (
                                        <ChatMessage
                                            message={item.text}
                                            isCurrentUser={item.senderId === user?.id}
                                            timestamp={item.timestamp}
                                            senderName={item.senderName}
                                        />
                                    )}
                                    contentContainerStyle={styles.messagesContainer}
                                    onScroll={handleScroll}
                                />
                                <ChatInput onSend={sendMessage} />
                            </>
                        ) : (
                            <View style={styles.noDoctorSelected}>
                                <Text>Select a doctor to start chatting</Text>
                            </View>
                        )}
                    </View>
                </>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'space-between'
    },
    leftPanel: {
        width: '30%',
        padding: 16
    },
    rightPanel: {
        flex: 1,
        padding: 16
    },
    doctorSearchContainer: {
        marginBottom: 16
    },
    doctorSearchInput: {
        padding: 8,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        marginBottom: 10
    },
    doctorCount: {
        color: '#888'
    },
    doctorList: {
        paddingBottom: 16
    },
    doctorItem: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        padding: 12,
        backgroundColor: '#f9f9f9',
        marginVertical: 5,
        borderRadius: 8
    },
    selectedDoctor: {
        backgroundColor: '#e0e0e0'
    },
    doctorName: {
        fontSize: 16
    },
    badge: {
        backgroundColor: theme.colors.error,
        borderRadius: 12,
        paddingVertical: 2,
        paddingHorizontal: 8
    },
    badgeText: {
        color: '#fff',
        fontSize: 12
    },
    fullWidthChatContainer: {
        flex: 1
    },
    chatHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#e1e1e1'
    },
    selectedDoctorName: {
        fontSize: 18,
        fontWeight: 'bold'
    },
    searchContainer: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    searchInputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#f5f5f5',
        borderRadius: 8,
        padding: 4
    },
    searchInput: {
        flex: 1,
        padding: 8,
        marginRight: 4,
        minWidth: 150
    },
    messagesContainer: {
        padding: 16
    },
    fullWidthDoctorList: {
        flex: 1,
        padding: 16
    },
    noDoctorSelected: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center'
    }
});