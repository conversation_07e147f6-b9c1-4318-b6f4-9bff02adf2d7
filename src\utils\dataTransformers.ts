/**
 * Utility functions for transforming data between backend and frontend formats
 */

/**
 * Transforms a MongoDB document to a standardized object with 'id' property
 * @param doc MongoDB document or any object with _id property
 * @returns Object with _id mapped to id and all other properties preserved, or undefined if input is falsy
 */
export const transformMongoDocument = <T extends { _id: string }>(
  doc: T | null | undefined
): (Omit<T, '_id'> & { id: string }) | undefined => {
  if (!doc) return undefined;

  // Extract _id and the rest of the properties
  const { _id, ...rest } = doc;

  // Return a new object with id instead of _id
  return {
    ...rest,
    id: _id,
  } as Omit<T, '_id'> & { id: string };
};

/**
 * Transforms an array of MongoDB documents to standardized objects
 * @param docs Array of MongoDB documents
 * @returns Array of objects with _id mapped to id
 */
export const transformMongoDocuments = <T extends { _id: string }>(
  docs: T[] | null | undefined
): (Omit<T, '_id'> & { id: string })[] => {
  if (!docs || !Array.isArray(docs)) return [];
  return docs.map(doc => transformMongoDocument(doc)).filter(Boolean) as (Omit<T, '_id'> & { id: string })[];
};

/**
 * Transforms a patient object to ensure it has a consistent id property
 * This handles both MongoDB documents and objects that might already have an id
 * @param patient Patient object that might have _id, id, or both
 * @returns Patient object with consistent id property, or undefined if input is falsy
 */
export const transformPatient = <T extends { _id?: string; id?: string }>(
  patient: T | null | undefined
): (Omit<T, '_id'> & { id: string }) | undefined => {
  if (!patient) return undefined;

  // If the patient already has both _id and id properties
  if (patient._id && patient.id) {
    // Ensure they are consistent, prioritize _id if they differ
    const id = patient._id;
    const { _id, ...rest } = patient;
    return { ...rest, id };
  }

  // If the patient only has _id, map it to id
  if (patient._id && !patient.id) {
    const { _id, ...rest } = patient;
    return { ...rest, id: _id };
  }

  // If the patient already has only id, return as is
  if (!patient._id && patient.id) {
    return patient as Omit<T, '_id'> & { id: string };
  }

  // If neither _id nor id exists (shouldn't happen), return as is with a warning
  console.warn('Patient object has no id or _id property:', patient);
  return { ...patient, id: 'unknown' } as Omit<T, '_id'> & { id: string };
};

/**
 * Transforms an array of patient objects to ensure they have consistent id properties
 * @param patients Array of patient objects
 * @returns Array of patient objects with consistent id properties
 */
export const transformPatients = <T extends { _id?: string; id?: string }>(
  patients: T[] | null | undefined
): (Omit<T, '_id'> & { id: string })[] => {
  if (!patients || !Array.isArray(patients)) return [];
  return patients.map(patient => transformPatient(patient)).filter(Boolean) as (Omit<T, '_id'> & { id: string })[];
};
