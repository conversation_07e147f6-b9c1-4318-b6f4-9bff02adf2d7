import React, { createContext, useContext, useState, useCallback } from 'react';
import { useAuth } from './AuthContext';
import { searchUsers } from '../utils/searchUtils';
import type {
  PatientProfile,
  MedicalHistory,
  Prescription,
  Appointment,
  Vitals,
  PatientNote,
  UpdatePatientData,
  CreateAppointmentData,
  CreatePrescriptionData,
  PatientFilters,
  AppointmentFilters,
  PrescriptionFilters,
} from '../types/patient';

interface PatientContextType {
  patients: PatientProfile[];
  selectedPatient: PatientProfile | null;
  medicalHistory: MedicalHistory[];
  prescriptions: Prescription[];
  appointments: Appointment[];
  vitals: Vitals[];
  notes: PatientNote[];
  isLoading: boolean;
  error: string | null;
  getPatients: (filters?: PatientFilters) => Promise<PatientProfile[]>;
  getPatientById: (id: string) => Promise<PatientProfile>;
  updatePatient: (id: string, data: UpdatePatientData) => Promise<void>;
  deletePatient: (id: string) => Promise<void>;
  getMedicalHistory: (patientId: string) => Promise<MedicalHistory[]>;
  addMedicalHistory: (patientId: string, data: Omit<MedicalHistory, 'id' | 'patientId'>) => Promise<void>;
  getPrescriptions: (filters?: PrescriptionFilters) => Promise<Prescription[]>;
  createPrescription: (patientId: string, data: CreatePrescriptionData) => Promise<void>;
  getAppointments: (filters?: AppointmentFilters) => Promise<Appointment[]>;
  createAppointment: (patientId: string, data: CreateAppointmentData) => Promise<void>;
  updateAppointment: (id: string, status: Appointment['status']) => Promise<void>;
  addVitals: (patientId: string, data: Omit<Vitals, 'id' | 'patientId' | 'recordedAt'>) => Promise<void>;
  getVitals: (patientId: string) => Promise<Vitals[]>;
  addNote: (patientId: string, data: Omit<PatientNote, 'id' | 'patientId' | 'authorId' | 'authorName' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  getNotes: (patientId: string) => Promise<PatientNote[]>;
}

const PatientContext = createContext<PatientContextType | undefined>(undefined);

const MOCK_PATIENTS: PatientProfile[] = [
  {
    id: 'pat1',
    userId: 'user1',
    name: 'John Doe',
    email: '<EMAIL>',
    dateOfBirth: '1990-01-15',
    gender: 'male',
    sex: 'male',
    bloodType: 'O+',
    height: 175,
    weight: 70,
    contactNumber: '+**********',
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    hospitalNo: ''
  },
];

const MOCK_MEDICAL_HISTORY: MedicalHistory[] = [
  {
    id: 'mh1',
    patientId: 'pat1',
    type: 'allergy',
    name: 'Penicillin Allergy',
    date: '2023-06-15',
    description: 'Severe allergic reaction to penicillin',
    status: 'active',
    severity: 'severe',
  },
];

const MOCK_PRESCRIPTIONS: Prescription[] = [
  {
    id: 'pres1',
    patientId: 'pat1',
    doctorId: 'doc1',
    doctorName: 'Dr. Smith',
    drugName: 'Amoxicillin',
    dosage: '500mg',
    frequency: 'Twice daily',
    duration: '7 days',
    startDate: '2024-01-15',
    endDate: '2024-01-22',
    instructions: 'Take with food',
    status: 'active',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
];

export const PatientProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [patients, setPatients] = useState<PatientProfile[]>(MOCK_PATIENTS);
  const [selectedPatient, setSelectedPatient] = useState<PatientProfile | null>(null);
  const [medicalHistory, setMedicalHistory] = useState<MedicalHistory[]>(MOCK_MEDICAL_HISTORY);
  const [prescriptions, setPrescriptions] = useState<Prescription[]>(MOCK_PRESCRIPTIONS);
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [vitals, setVitals] = useState<Vitals[]>([]);
  const [notes, setNotes] = useState<PatientNote[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getPatients = useCallback(async (filters?: PatientFilters): Promise<PatientProfile[]> => {
    try {
      setIsLoading(true);
      setError(null);

      await new Promise(resolve => setTimeout(resolve, 1000));

      let filteredPatients = [...patients];

      if (filters) {
        if (filters.status) {
          filteredPatients = filteredPatients.filter(p => p.status === filters.status);
        }
        if (filters.doctorId) {
        }
        if (filters.searchQuery) {
          // Use the enhanced search utility function
          filteredPatients = searchUsers(filteredPatients, filters.searchQuery, ['contactNumber', 'hospitalNo']);
        }
      }

      return filteredPatients;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch patients');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [patients]);

  const getPatientById = useCallback(async (id: string): Promise<PatientProfile> => {
    try {
      setIsLoading(true);
      setError(null);

      const patient = patients.find(p => p.id === id);
      if (!patient) {
        throw new Error('Patient not found');
      }

      setSelectedPatient(patient);
      return patient;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch patient');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [patients]);

  const updatePatient = useCallback(async (id: string, data: UpdatePatientData): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);

      await new Promise(resolve => setTimeout(resolve, 1000));

      setPatients(prev => prev.map(patient =>
        patient.id === id
          ? { ...patient, ...data, updatedAt: new Date().toISOString() }
          : patient
      ));

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update patient');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const deletePatient = useCallback(async (id: string): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);

      const filteredPatients = patients.filter(patient => patient.id !== id);
      setPatients(filteredPatients);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete patient');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [patients]);

  return (
    <PatientContext.Provider
      value={{
        patients,
        selectedPatient,
        medicalHistory,
        prescriptions,
        appointments,
        vitals,
        notes,
        isLoading,
        error,
        deletePatient,
        getPatients,
        getPatientById,
        updatePatient,
        getMedicalHistory: async () => medicalHistory,
        addMedicalHistory: async () => {},
        getPrescriptions: async () => prescriptions,
        createPrescription: async () => {},
        getAppointments: async () => appointments,
        createAppointment: async () => {},
        updateAppointment: async () => {},
        addVitals: async () => {},
        getVitals: async () => vitals,
        addNote: async () => {},
        getNotes: async () => notes,
      }}
    >
      {children}
    </PatientContext.Provider>
  );
};

export const usePatient = () => {
  const context = useContext(PatientContext);
  if (context === undefined) {
    throw new Error('usePatient must be used within a PatientProvider');
  }
  return context;
};
