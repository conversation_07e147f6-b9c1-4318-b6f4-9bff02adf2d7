const express = require('express');
const router = express.Router();
const Patient = require('../models/User');
const auth = require('../middleware/auth');
const User = require('../models/User');

router.get('/', async (req, res) => {
  try {
    const patients = await Patient.find({ role: 'patient' });
    res.json(patients);
  } catch (error) {
    console.error('Error fetching patients:', error);
    res.status(500).json({ error: 'Failed to fetch patients' });
  }
});

router.put('/update-profile/:patientId', auth, async (req, res) => {
  try {
    const { patientId } = req.params;

    // Check if the user has permission (admin, drug-counsellor, or the patient themselves)
    if (req.user.role !== 'drug-counsellor' &&
        req.user.role !== 'admin' &&
        req.user._id.toString() !== patientId) {
      return res.status(403).json({ error: 'You do not have permission to update patient profiles' });
    }

    // Get the data to update from the request body
    const updateFields = { ...req.body };

    // If either firstName or lastName is being updated, update both the individual fields and the combined name
    if (updateFields.firstName || updateFields.lastName) {
      const currentPatient = await User.findById(patientId);
      if (!currentPatient) {
        return res.status(404).json({ error: 'Patient not found' });
      }

      // Use the new values if provided, otherwise keep the current values
      updateFields.firstName = updateFields.firstName ?? currentPatient.firstName;
      updateFields.lastName = updateFields.lastName ?? currentPatient.lastName;
      // Update the combined name field
      updateFields.name = `${updateFields.firstName} ${updateFields.lastName}`;
    }

    // Update the patient data with all fields
    const updatedPatient = await User.findByIdAndUpdate(
      patientId,
      updateFields,
      { new: true }
    );

    if (!updatedPatient) {
      return res.status(404).json({ error: 'Patient not found' });
    }

    res.json({ user: updatedPatient });
  } catch (error) {
    console.error('Error updating profile:', error);
    res.status(500).json({ error: 'Server error while updating profile' });
  }
});
router.delete('/:patientId', auth, async (req, res) => {
  try {
    const { patientId } = req.params;
    console.log('DELETE request received for patientId:', patientId);

    console.log('Authenticated user:', req.user._id, 'Role:', req.user.role);

    if (req.user.role !== 'admin' && req.user.role !== 'drug-counsellor') {
      console.log('Permission denied: not admin or drug-counsellor');
      return res.status(403).json({ error: 'You do not have permission to delete this patient' });
    }

    const deletedPatient = await User.findByIdAndDelete(patientId);
    if (!deletedPatient) {
      console.log('No user found with ID:', patientId);
      return res.status(404).json({ error: 'Patient not found' });
    }

    console.log('Patient deleted:', deletedPatient._id);
    res.status(200).json({ message: 'Patient deleted successfully' });
  } catch (error) {
    console.error('Error deleting patient:', error);
    res.status(500).json({ error: 'Server error while deleting patient' });
  }
});

// Get patient demographics for analytics
router.get('/demographics', auth, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ error: 'You do not have permission to access demographics data' });
    }

    const patients = await User.find({ role: 'patient' });

    // Gender distribution
    const genderDistribution = {
      Male: 0,
      Female: 0,
      Other: 0
    };

    // Age group distribution with 3-year intervals
    const ageGroupDistribution = {
      '0-2': 0,
      '3-5': 0,
      '6-8': 0,
      '9-11': 0,
      '12-14': 0,
      '15-17': 0,
      '18-20': 0,
      '21-23': 0,
      '24-26': 0,
      '27-29': 0,
      '30-32': 0,
      '33-35': 0,
      '36-38': 0,
      '39-41': 0,
      '42-44': 0,
      '45-47': 0,
      '48-50': 0,
      '51-53': 0,
      '54-56': 0,
      '57-59': 0,
      '60-62': 0,
      '63-65': 0,
      '66-68': 0,
      '69-71': 0,
      '72+': 0
    };

    // Ethnicity distribution
    const ethnicityDistribution = {};

    patients.forEach(patient => {
      // Count gender
      if (patient.sex === 'Male') {
        genderDistribution.Male += 1;
      } else if (patient.sex === 'Female') {
        genderDistribution.Female += 1;
      } else {
        genderDistribution.Other += 1;
      }

      // Count age groups with 3-year intervals
      const age = patient.age;
      if (age <= 2) {
        ageGroupDistribution['0-2'] += 1;
      } else if (age <= 5) {
        ageGroupDistribution['3-5'] += 1;
      } else if (age <= 8) {
        ageGroupDistribution['6-8'] += 1;
      } else if (age <= 11) {
        ageGroupDistribution['9-11'] += 1;
      } else if (age <= 14) {
        ageGroupDistribution['12-14'] += 1;
      } else if (age <= 17) {
        ageGroupDistribution['15-17'] += 1;
      } else if (age <= 20) {
        ageGroupDistribution['18-20'] += 1;
      } else if (age <= 23) {
        ageGroupDistribution['21-23'] += 1;
      } else if (age <= 26) {
        ageGroupDistribution['24-26'] += 1;
      } else if (age <= 29) {
        ageGroupDistribution['27-29'] += 1;
      } else if (age <= 32) {
        ageGroupDistribution['30-32'] += 1;
      } else if (age <= 35) {
        ageGroupDistribution['33-35'] += 1;
      } else if (age <= 38) {
        ageGroupDistribution['36-38'] += 1;
      } else if (age <= 41) {
        ageGroupDistribution['39-41'] += 1;
      } else if (age <= 44) {
        ageGroupDistribution['42-44'] += 1;
      } else if (age <= 47) {
        ageGroupDistribution['45-47'] += 1;
      } else if (age <= 50) {
        ageGroupDistribution['48-50'] += 1;
      } else if (age <= 53) {
        ageGroupDistribution['51-53'] += 1;
      } else if (age <= 56) {
        ageGroupDistribution['54-56'] += 1;
      } else if (age <= 59) {
        ageGroupDistribution['57-59'] += 1;
      } else if (age <= 62) {
        ageGroupDistribution['60-62'] += 1;
      } else if (age <= 65) {
        ageGroupDistribution['63-65'] += 1;
      } else if (age <= 68) {
        ageGroupDistribution['66-68'] += 1;
      } else if (age <= 71) {
        ageGroupDistribution['69-71'] += 1;
      } else {
        ageGroupDistribution['72+'] += 1;
      }

      // Count ethnicities
      if (patient.ethnicity) {
        const ethnicity = patient.ethnicity.trim();
        if (ethnicity) {
          if (ethnicityDistribution[ethnicity]) {
            ethnicityDistribution[ethnicity] += 1;
          } else {
            ethnicityDistribution[ethnicity] = 1;
          }
        }
      }
    });

    res.json({
      genderDistribution,
      ageGroupDistribution,
      ethnicityDistribution,
      totalPatients: patients.length
    });
  } catch (error) {
    console.error('Error fetching patient demographics:', error);
    res.status(500).json({ error: 'Failed to fetch patient demographics' });
  }
});

module.exports = router;
