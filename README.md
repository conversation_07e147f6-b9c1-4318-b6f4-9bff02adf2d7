# Drug Counselling Application

A cross-platform (iOS, Android, Web) application for managing drug counselling services.

## Features

- Multi-role authentication (<PERSON><PERSON>, Doctor, Drug Counsellor, Patient)
- Patient registration and management
- Drug reaction reporting system
- Real-time chat between patients and drug counsellors
- Staff management for administrators
- Secure authentication and authorization

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- npm or yarn
- Expo CLI
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

### Installation

1. Clone the repository
2. Install dependencies:
```bash
cd DrugCounsellingApp
npm install
```

### Running the Application

1. Start the development server:
```bash
npx expo start
```

2. Use the Expo Go app on your mobile device to scan the QR code, or press:
   - `a` for Android emulator
   - `i` for iOS simulator
   - `w` for web browser

### Default Login Credentials

For testing purposes, the following accounts are available:

1. Admin Account:
   - Email: <EMAIL>
   - Password: admin123

2. Doctor Account:
   - Email: <EMAIL>
   - Password: doctor123

3. Drug Counsellor Account:
   - Email: <EMAIL>
   - Password: counsellor123

4. Patient Account:
   - Email: <EMAIL>
   - Password: patient123

### User Registration

- Only patients can register directly through the application
- Doctors and Drug Counsellors must be created by an administrator
- Admin accounts can only be created through the system script

## Application Structure

```
DrugCounsellingApp/
├── src/
│   ├── screens/
│   │   ├── admin/         # Admin-specific screens
│   │   ├── auth/          # Authentication screens
│   │   ├── counsellor/    # Drug counsellor screens
│   │   ├── doctor/        # Doctor screens
│   │   └── user/          # Patient screens
│   ├── navigation/        # Navigation configuration
│   ├── context/          # Context providers
│   ├── theme/            # Theme and styling
│   └── types/            # TypeScript type definitions
├── scripts/              # Utility scripts
└── README.md
```

## Security Features

- Role-based access control
- Secure password handling
- Protected routes based on user roles
- Input validation and sanitization
- Error handling and validation messages

## Color Scheme

The application uses a professional medical-themed color palette:
- Primary Color: #00A77E
- Secondary Colors: Various shades of green and blue
- Error Color: #DC3545
- Background: #F8F9FA
- Surface: #FFFFFF

## Development Notes

1. Patient Registration:
   - Only allows patient registration
   - Validates email format and password strength
   - Automatic role assignment as 'user'

2. Staff Management:
   - Admin can create and manage doctors and drug counsellors
   - Staff status tracking (active/inactive)
   - Staff specialization and details management

3. Authentication:
   - JWT-based authentication (to be implemented)
   - Persistent sessions using AsyncStorage
   - Secure password handling

4. Future Implementations:
   - Real-time chat system
   - Push notifications
   - Drug reaction reporting system
   - Patient history management
   - Analytics dashboard

## Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a new Pull Request

