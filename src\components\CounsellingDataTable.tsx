import React from 'react';
import { CounsellingData } from '../types/counsellingData';
import './CounsellingDataTable.css';

interface CounsellingDataTableProps {
    data: CounsellingData[];
    onRowSelect: (selectedItem: CounsellingData) => void;
    selectedRows: string[];
}

const CounsellingDataTable: React.FC<CounsellingDataTableProps> = ({ data, onRowSelect, selectedRows }) => {
    return (
        <div className="data-table-container">
            <table className="counselling-data-table">
                <thead>
                    <tr>
                        <th>S.No</th> 
                        <th>Date</th>
                        <th>Hospital No</th>
                        <th>Patient Name</th>
                        <th>Age/Sex</th>
                        <th>Address</th>
                        <th>Contact No</th>
                        <th>Diagnosis</th>
                        <th>Medical History</th>
                        <th>Medication History</th>
                        <th>Allergy History</th>
                        <th>Alcohol Intake</th>
                        <th>Smoking History</th>
                        <th>Prescribed Medicine</th>
                        <th>Counseling Provided</th>
                        <th>Average Counseling Time</th>
                        <th>Department</th>
                        <th>Created By</th>
                    </tr>
                </thead>
                <tbody>
                    {data.slice().reverse().map((item, index) => (
                <tr key={item._id}>
                            <td>
                                <input
                                    type="checkbox"
                                    checked={selectedRows.includes(String(item._id))}
                                    onChange={() => onRowSelect(item)} // Pass the whole item (CounsellingData)
                                />
                            </td>
                            <td>{new Date(item.date).toLocaleDateString()}</td>
                            <td>{item.hospitalNo}</td>
                            <td>{item.patientName}</td>
                            <td>{item.ageSex}</td>
                            <td>{item.address}</td>
                            <td>{item.contactNo}</td>
                            <td>{item.diagnosis}</td>
                            <td>{item.medicalHistory}</td>
                            <td>{item.medicationHistory}</td>
                            <td>{item.allergyHistory}</td>
                            <td>{item.alcoholIntake}</td>
                            <td>{item.smokingHistory}</td>
                            <td>{item.prescribedMedicine}</td>
                            <td>{item.counselingProvided}</td>
                            <td>{typeof item.averageCounselingTime === 'number' ? item.averageCounselingTime : 0}</td>
                            <td>{item.department}</td>
                            <td>{item.createdBy}</td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default CounsellingDataTable;
