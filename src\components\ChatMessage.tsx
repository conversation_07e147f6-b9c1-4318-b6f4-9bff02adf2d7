import React, { useState } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, Linking, Platform } from 'react-native';
import { useTheme, Portal, Modal, IconButton, Button } from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { ChatMessageProps } from '../types/chat';
import { WebView } from 'react-native-webview';

const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  isCurrentUser,
  timestamp,
  senderName,
  status,
  attachment
}) => {
  const theme = useTheme();
  const [showFullImage, setShowFullImage] = useState(false);
  const [showDocumentPreview, setShowDocumentPreview] = useState(false);
  const [documentUrl, setDocumentUrl] = useState<string>('');

  const getStatusIcon = () => {
    if (!isCurrentUser) return null;

    switch (status) {
      case 'sent':
        return <MaterialIcons name="check" size={12} color="#ffffffaa" />;
      case 'failed':
        return <MaterialIcons name="error" size={12} color="#ff4444" />;
      default:
        return null;
    }
  };

  // Check for URL-based images and files (legacy support)
  const isUrlImage = message?.startsWith('http') && (message.endsWith('.jpg') || message.endsWith('.png') || message.endsWith('.jpeg'));
  const isUrlFile = message?.startsWith('http') && !isUrlImage;

  // Get file icon based on mime type
  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'videocam';
    if (mimeType.startsWith('audio/')) return 'audiotrack';
    if (mimeType.includes('pdf')) return 'picture-as-pdf';
    if (mimeType.includes('word') || mimeType.includes('document')) return 'description';
    if (mimeType.includes('excel') || mimeType.includes('sheet')) return 'table-chart';
    if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return 'slideshow';
    return 'insert-drive-file';
  };

  // Handle file preview/download
  const handleFilePress = () => {
    if (!attachment) return;

    // Create blob URL for the file
    if (Platform.OS === 'web') {
      // Convert base64 to binary using browser APIs instead of Buffer
      const binaryString = window.atob(attachment.fileData);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      const blob = new Blob([bytes], { type: attachment.mimeType });
      const url = URL.createObjectURL(blob);

      // For PDFs, show preview
      if (attachment.mimeType === 'application/pdf') {
        setDocumentUrl(url);
        setShowDocumentPreview(true);
      } else {
        // For other file types, download
        const a = document.createElement('a');
        a.href = url;
        a.download = attachment.fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    } else {
      // For mobile platforms
      if (attachment.mimeType === 'application/pdf') {
        // For PDFs on mobile, we'll use a data URI with the WebView
        const base64Data = attachment.fileData;
        const dataUri = `data:${attachment.mimeType};base64,${base64Data}`;
        setDocumentUrl(dataUri);
        setShowDocumentPreview(true);
      } else {
        // For other file types on mobile
        alert('File download not implemented for mobile platforms in this example');
      }
    }
  };

  return (
    <View style={[
      styles.container,
      {
        alignSelf: isCurrentUser ? 'flex-end' : 'flex-start',
        backgroundColor: isCurrentUser ? theme.colors.primary : '#e1e1e1',
      }
    ]}>
      {!isCurrentUser && (
        <Text style={styles.senderName}>{senderName}</Text>
      )}

      {/* Text message */}
      {message && !isUrlImage && !isUrlFile && (
        <Text style={[styles.messageText, { color: isCurrentUser ? '#fff' : '#000' }]}>
          {message}
        </Text>
      )}

      {/* Legacy URL-based images */}
      {isUrlImage && (
        <Image
          source={{ uri: message }}
          style={styles.image}
          resizeMode="cover"
        />
      )}

      {/* Legacy URL-based files */}
      {isUrlFile && (
        <TouchableOpacity onPress={() => Linking.openURL(message)}>
          <Text style={[styles.fileLink, { color: isCurrentUser ? '#fff' : theme.colors.primary }]}>
            📎 File Attachment
          </Text>
        </TouchableOpacity>
      )}

      {/* Image attachment */}
      {attachment && attachment.fileType === 'image' && (
        <TouchableOpacity onPress={() => setShowFullImage(true)}>
          <Image
            source={{ uri: `data:${attachment.mimeType};base64,${attachment.fileData}` }}
            style={styles.image}
            resizeMode="cover"
          />
          <Text style={[styles.fileInfo, { color: isCurrentUser ? '#fff' : '#000' }]}>
            {(attachment.fileSize / 1024).toFixed(1)} KB
          </Text>
        </TouchableOpacity>
      )}

      {/* Document attachment */}
      {attachment && attachment.fileType !== 'image' && (
        <TouchableOpacity onPress={handleFilePress} style={styles.fileContainer}>
          <MaterialIcons
            name={getFileIcon(attachment.mimeType)}
            size={24}
            color={isCurrentUser ? '#fff' : theme.colors.primary}
          />
          <View style={styles.fileDetails}>
            <Text
              style={[styles.fileName, { color: isCurrentUser ? '#fff' : '#000' }]}
              numberOfLines={1}
            >
              {attachment.fileName}
            </Text>
            <View style={styles.fileInfoRow}>
              <Text style={[styles.fileInfo, { color: isCurrentUser ? '#fff' : '#000' }]}>
                {(attachment.fileSize / 1024).toFixed(1)} KB
              </Text>
              {attachment.mimeType === 'application/pdf' && (
                <Text style={[styles.previewLabel, { color: isCurrentUser ? '#fff' : theme.colors.primary }]}>
                  Tap to preview
                </Text>
              )}
            </View>
          </View>
        </TouchableOpacity>
      )}

      <View style={styles.timestampContainer}>
        <Text style={styles.timestamp}>
          {new Date(timestamp).toLocaleTimeString()}
        </Text>
        {getStatusIcon()}
      </View>

      {/* Full image modal */}
      {attachment && attachment.fileType === 'image' && (
        <Portal>
          <Modal
            visible={showFullImage}
            onDismiss={() => setShowFullImage(false)}
            contentContainerStyle={styles.modalContainer}
          >
            <View style={styles.imageModalContent}>
              <Image
                source={{ uri: `data:${attachment.mimeType};base64,${attachment.fileData}` }}
                style={styles.fullImage}
                resizeMode="contain"
              />
            </View>

            <IconButton
              icon="close"
              size={24}
              onPress={() => setShowFullImage(false)}
              style={styles.closeButton}
            />
          </Modal>
        </Portal>
      )}

      {/* Document preview modal */}
      <Portal>
        <Modal
          visible={showDocumentPreview}
          onDismiss={() => {
            setShowDocumentPreview(false);
            // Clean up URL object when modal is closed (for web)
            if (Platform.OS === 'web' && documentUrl.startsWith('blob:')) {
              URL.revokeObjectURL(documentUrl);
            }
          }}
          contentContainerStyle={styles.documentModalContainer}
        >
          {Platform.OS === 'web' ? (
            <>
              <iframe
                src={documentUrl}
                style={{...styles.documentViewer, border: 'none'}}
                title="Document Preview"
              />
              <View style={styles.documentTitleContainer}>
                <Text style={styles.documentTitle}>
                  {attachment?.fileName || 'Document Preview'}
                </Text>

                {/* Download button for documents */}
                <TouchableOpacity
                  style={styles.documentDownloadButton}
                  onPress={() => {
                    if (!attachment) return;

                    try {
                      // Create a download link for the document
                      // Use a more reliable method to convert base64 to blob
                      const base64Data = attachment.fileData;
                      const dataUrl = `data:${attachment.mimeType};base64,${base64Data}`;

                      fetch(dataUrl)
                        .then(res => res.blob())
                        .then(blob => {
                          // Create a download link
                          const url = URL.createObjectURL(blob);
                          const a = document.createElement('a');
                          a.href = url;

                          // Use the original file name if available
                          if (attachment.fileName) {
                            a.download = attachment.fileName;
                          } else {
                            // Fallback to creating a name based on MIME type
                            const extension = attachment.mimeType.split('/')[1] || 'pdf';
                            a.download = `document.${extension}`;
                          }

                          // Trigger download
                          document.body.appendChild(a);
                          a.click();
                          document.body.removeChild(a);

                          // Clean up
                          setTimeout(() => {
                            URL.revokeObjectURL(url);
                          }, 100);
                        })
                        .catch(err => {
                          console.error('Error downloading document:', err);
                          alert('Failed to download document. Please try again.');
                        });
                    } catch (error) {
                      console.error('Error in download process:', error);
                      alert('Failed to download document. Please try again.');
                    }
                  }}
                >
                  <MaterialIcons name="file-download" size={20} color="#333" />
                </TouchableOpacity>
              </View>
            </>
          ) : (
            <WebView
              source={{ uri: documentUrl }}
              style={styles.documentViewer}
            />
          )}
          <IconButton
            icon="close"
            size={24}
            onPress={() => {
              setShowDocumentPreview(false);
              // Clean up URL object when modal is closed (for web)
              if (Platform.OS === 'web' && documentUrl.startsWith('blob:')) {
                URL.revokeObjectURL(documentUrl);
              }
            }}
            style={styles.closeButton}
          />
        </Modal>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 12,
    marginBottom: 8,
  },
  messageText: {
    fontSize: 16,
  },
  senderName: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  image: {
    width: 180,
    height: 180,
    borderRadius: 8,
    marginBottom: 4,
  },
  fileLink: {
    textDecorationLine: 'underline',
    fontSize: 16,
    marginBottom: 4,
  },
  timestampContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    gap: 4,
    marginTop: 4,
  },
  timestamp: {
    fontSize: 10,
    color: '#666',
  },
  fileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 8,
    padding: 8,
    marginVertical: 4,
  },
  fileDetails: {
    marginLeft: 8,
    flex: 1,
  },
  fileName: {
    fontSize: 14,
    fontWeight: '500',
  },
  fileInfo: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  fileInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  previewLabel: {
    fontSize: 12,
    fontStyle: 'italic',
    marginLeft: 8,
  },
  modalContainer: {
    backgroundColor: 'rgba(0,0,0,0.9)',
    flex: 1,
    margin: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullImage: {
    width: '90%',
    height: '80%',
    borderRadius: 8,
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 20,
  },
  documentModalContainer: {
    backgroundColor: 'white',
    flex: 1,
    margin: 20,
    borderRadius: 8,
    overflow: 'hidden',
    maxHeight: '80%',
  },
  documentViewer: {
    width: '100%',
    height: '100%',
    borderWidth: 0,
  },
  documentTitleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    paddingHorizontal: 16,
    paddingVertical: 10,
  },
  documentTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
  },
  documentDownloadButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: '#e0e0e0',
  },
  imageModalContent: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },

});

export default ChatMessage;
