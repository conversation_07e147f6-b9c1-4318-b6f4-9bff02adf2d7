export type Gender = 'male' | 'female' | 'other';
export type BloodType = 'A+' | 'A-' | 'B+' | 'B-' | 'AB+' | 'AB-' | 'O+' | 'O-';
export type PatientStatus = 'active' | 'inactive';

export interface PatientProfile {
  id: string;
  userId: string;
  name: string;
  email: string;
  dateOfBirth: string;
  gender: Gender;
  sex: string;
  bloodType?: BloodType;
  height?: number; 
  weight?: number;
  contactNumber?: string;
  hospitalNo: string;
  emergencyContact?: {
    name: string;
    relationship: string;
    phone: string;
  };
  address?: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  status: PatientStatus;
  createdAt: string;
  updatedAt: string;
}

export interface MedicalHistory {
  id: string;
  patientId: string;
  type: 'condition' | 'surgery' | 'allergy' | 'immunization';
  name: string;
  date: string;
  description?: string;
  status: 'active' | 'resolved';
  severity?: 'mild' | 'moderate' | 'severe';
  notes?: string;
}

export interface Prescription {
  id: string;
  patientId: string;
  doctorId: string;
  doctorName: string;
  drugName: string;
  dosage: string;
  frequency: string;
  duration: string;
  startDate: string;
  endDate: string;
  instructions?: string;
  status: 'active' | 'completed' | 'cancelled';
  createdAt: string;
  updatedAt: string;
}

export interface Appointment {
  id: string;
  patientId: string;
  doctorId: string;
  doctorName: string;
  date: string;
  time: string;
  duration: number; // in minutes
  type: 'regular' | 'follow-up' | 'emergency';
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled';
  reason?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Vitals {
  id: string;
  patientId: string;
  recordedAt: string;
  bloodPressure?: {
    systolic: number;
    diastolic: number;
  };
  heartRate?: number;
  temperature?: number;
  respiratoryRate?: number;
  oxygenSaturation?: number;
  recordedBy: {
    id: string;
    name: string;
    role: 'doctor' | 'nurse';
  };
}

export interface PatientNote {
  id: string;
  patientId: string;
  authorId: string;
  authorName: string;
  authorRole: 'doctor' | 'drug-counsellor';
  content: string;
  category: 'general' | 'diagnosis' | 'treatment' | 'follow-up';
  visibility: 'private' | 'team' | 'patient';
  createdAt: string;
  updatedAt: string;
}

export interface UpdatePatientData {
  name?: string;
  hospitalNo?: string;
  firstName?: string;
  lastName?: string;
  age?: number;
  sex?: string;
  bloodType?: BloodType;
  height?: number;
  weight?: number;
  contactNumber?: string;
  ethnicity?: string;
  pregnant?: string;
  dateOfBirth?: string;
  priorCounsel?: string;
  emergencyContact?: {
    name: string;
    relationship: string;
    phone: string;
  };
  address?: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
}

export interface CreateAppointmentData {
  doctorId: string;
  date: string;
  time: string;
  duration: number;
  type: 'regular' | 'follow-up' | 'emergency';
  reason?: string;
}

export interface CreatePrescriptionData {
  drugName: string;
  dosage: string;
  frequency: string;
  duration: string;
  startDate: string;
  endDate: string;
  instructions?: string;
}

export interface PatientFilters {
  status?: PatientStatus;
  doctorId?: string;
  searchQuery?: string;
  dateRange?: {
    start: string;
    end: string;
  };
}

export interface AppointmentFilters {
  status?: Appointment['status'];
  type?: Appointment['type'];
  doctorId?: string;
  dateRange?: {
    start: string;
    end: string;
  };
}

export interface PrescriptionFilters {
  status?: Prescription['status'];
  doctorId?: string;
  dateRange?: {
    start: string;
    end: string;
  };
}
