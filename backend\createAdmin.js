require('dotenv').config();
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('./models/User');

async function createAdmin(name, email, password) {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGO_URI || 'mongodb://127.0.0.1:27017/drugCounselling', {
            useNewUrlParser: true,
            useUnifiedTopology: true
        });

        // Check if admin already exists
        const existingAdmin = await User.findOne({ email });
        if (existingAdmin) {
            console.log('Admin with this email already exists.');
            return;
        }

        // Hash password
        const hashedPassword = await bcrypt.hash(password, 8);

        // Create new admin user
        const admin = new User({
            username:email,
            name,
            email,
            password: hashedPassword,
            role: 'admin'
        });

        await admin.save();
        console.log(`Admin user '${name}' created successfully.`);

    } catch (error) {
        console.error('Error creating admin:', error);
    } finally {
        // Close the database connection
        await mongoose.connection.close();
    }
}

// Create admin user - Replace these values with actual admin details
createAdmin(
    'Admin User',
    '<EMAIL>',
    'securePassword123'
);
