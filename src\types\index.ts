export interface User {
  id: string; // Primary identifier used throughout the application
  sex: string;
  pregnant: string;
  priorCounsel: string;
  dateOfBirth: string;
  hospitalNo: string;
  firstName: string;
  lastName: string;
  age: number;
  weight: any;
  contactNumber: string;
  ethnicity: string;
  email: string;
  name: string;
  username?: string;
  role: 'patient' | 'doctor' | 'drug-counsellor' | 'admin';
  createdAt?: string;
  status?: 'active' | 'inactive';
  phoneNumber: string;
  designation: string;
  department: string;
  specialization?: string;
  // MongoDB _id is removed from the interface as we standardize on id
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;
}

export interface RegisterData {
  email: string;
  password: string;
  username: string;
  hospitalNo: string;
  firstName: string;
  lastName: string;
  age: number;
  sex: string;
  weight: number;
  contactNumber: string;
  ethnicity: string;
  pregnant: string;
  dateOfBirth: string;
  priorCounsel: string;
  role?: 'patient' | 'doctor' | 'drug-counsellor' | 'admin';
}
export interface UpdateRegisterData {
  email?: string;
  password?: string;
  username?: string;
  hospitalNo?: string;
  firstName?: string;
  lastName?: string;
  age?: number;
  sex?: string;
  weight?: number;
  contactNumber?: string;
  ethnicity?: string;
  pregnant?: string;
  dateOfBirth?: string;
  priorCounsel?: string;
  role?: 'patient' | 'doctor' | 'drug-counsellor' | 'admin';
}


export interface LoginData {
  email: string;
  password: string;
}

export interface StaffMember {
  id: string; // Primary identifier used throughout the application
  name: string;
  email: string;
  role: 'doctor' | 'drug-counsellor';
  specialization?: string;
  status: 'active' | 'inactive';
  createdAt: string;
  lastActive?: string;
  phoneNumber: string;
  department: string;
  designation: string;
  // MongoDB _id is removed from the interface as we standardize on id
}

export interface CreateStaffData {
  email: string;
  name: string;
  role: 'doctor' | 'drug-counsellor';
  specialization?: string;
  password: string;
  phoneNumber: string;
  designation: string;
  department: string;
}

export interface UpdateStaffData {
  name?: string;
  email?: string;
  role?: 'doctor' | 'drug-counsellor';
  specialization?: string;
  phoneNumber?: string;
  designation?: string;
  department?: string;
  password?: string; // Optional password for updates
}

export interface Patient {
  id: string; // Primary identifier used throughout the application
  _id?: string; // MongoDB ID, included for compatibility with backend responses
  hospitalNo: string;
  name: string;
  firstName: string;
  lastName: string;
  email: string;
  age?: number;
  sex?: string;
  contactNumber?: string;
  address?: string;
  medicalHistory?: string[];
  assignedDoctor?: string;
  status: 'active' | 'inactive';
  createdAt: string;
  // Additional fields that were missing
  dateOfBirth?: string;
  weight?: number;
  ethnicity?: string;
  priorCounsel?: string;
  pregnant?: string;
  updatedAt?: string;
}

export interface DrugReaction {
  id: string;
  patientId: string;
  drugName: string;
  reaction: string;
  severity: 'mild' | 'moderate' | 'severe';
  dateReported: string;
  status: 'pending' | 'in-progress' | 'resolved';
  assignedCounsellor?: string;
  notes?: string;
}

export interface Notification {
  id: string;
  type: 'reaction' | 'chat' | 'system';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  data?: {
    reactionId?: string;
    chatId?: string;
    userId?: string;
  };
}
