import React, { useEffect, useState } from 'react';
import { View, StyleSheet, ScrollView, ActivityIndicator, Dimensions, Platform } from 'react-native';
import { Text, Button, Card, IconButton, Surface, Dialog, Portal, Paragraph, Avatar, useTheme } from 'react-native-paper';
import { theme, styles as globalStyles } from '../../theme';
import { useAuth, API_BASE_URL } from '../../context/AuthContext';
import { DrugReaction } from '../../types/drugReaction';
import axios from 'axios';
import { useFocusEffect } from '@react-navigation/native'; // Import useFocusEffect

// Simple responsive breakpoint - consistent with other dashboard components
const SCREEN_BREAKPOINT_TABLET = 768;

const getStatusColor = (status: string) => {
    switch (status) {
        case 'pending':
            return theme.colors.error;
        case 'resolved':
            return theme.colors.secondary;
        default:
            return theme.colors.text;
    }
};

const StatCard = ({ number, label, styles }: { number: number; label: string; styles: any }) => (
    <Surface style={styles.statCard}>
        <Text style={styles.statNumber}>{number}</Text>
        <Text style={styles.statLabel}>{label}</Text>
    </Surface>
);

export const UserDashboard = ({ navigation }: any) => {
    const { user, logout } = useAuth();
    const paperTheme = useTheme(); // Add useTheme hook for accessing theme in the component
    const [screenWidth, setScreenWidth] = useState(Dimensions.get('window').width);
    const styles = React.useMemo(() => createStyles(paperTheme, screenWidth), [paperTheme, screenWidth]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [stats, setStats] = useState({
        total: 0,
        pending: 0,
        resolved: 0
    });
    const [recentReport, setRecentReport] = useState<DrugReaction | null>(null);
    const [logoutDialogVisible, setLogoutDialogVisible] = useState(false);

    // Update screen width when dimensions change
    useEffect(() => {
        const updateLayout = () => {
            setScreenWidth(Dimensions.get('window').width);
        };

        // Add event listener for dimension changes
        const subscription = Dimensions.addEventListener('change', updateLayout);

        // Clean up event listener on component unmount
        return () => subscription.remove();
    }, []);

    const fetchStats = async () => {
        if (!user) {
            setError('User not found');
            setLoading(false);
            return;
        }

        try {
            const response = await axios.get(`${API_BASE_URL}/api/drug-reactions/user/${user.id}`);
            const reactions: DrugReaction[] = response.data;

            setStats({
                total: reactions.length,
                pending: reactions.filter(r => r.status === 'pending').length,
                resolved: reactions.filter(r => r.status === 'resolved').length
            });

            if (reactions.length > 0) {
                const sortedReactions = reactions.sort(
                    (a, b) => new Date(b.dateReported).getTime() - new Date(a.dateReported).getTime()
                );
                setRecentReport(sortedReactions[0]);
            }
        } catch (err) {
            setError('Error fetching statistics');
            console.error(err);
        } finally {
            setLoading(false);
        }
    };

    useFocusEffect(
        React.useCallback(() => {
            fetchStats();
        }, [])
    );

    const renderStats = () => {
        if (loading) {
            return (
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={paperTheme.colors.primary} />
                </View>
            );
        }

        return (
            <View style={styles.statsContainer}>
                <StatCard number={stats.total} label="Total Reports" styles={styles} />
                <StatCard number={stats.pending} label="Pending" styles={styles} />
                <StatCard number={stats.resolved} label="Resolved" styles={styles} />
            </View>
        );
    };

    // Function to show the logout confirmation dialog
    const showLogoutDialog = () => {
        setLogoutDialogVisible(true);
    };

    // Function to handle logout confirmation
    const handleLogoutConfirm = async () => {
        setLogoutDialogVisible(false);
        await logout();
    };

    // Function to dismiss the logout dialog
    const hideLogoutDialog = () => {
        setLogoutDialogVisible(false);
    };

    const renderRecentReport = () => {
        if (loading) {
            return (
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={paperTheme.colors.primary} />
                </View>
            );
        }

        if (!recentReport) {
            return (
                <View style={styles.emptyContainer}>
                    <IconButton
                        icon="clipboard-alert-outline"
                        size={48}
                        iconColor={paperTheme.colors.outline}
                    />
                    <Text style={styles.emptyText}>No reactions reported yet</Text>
                    <Button
                        mode="contained"
                        icon="plus"
                        onPress={() => navigation.navigate('ReportReaction')}
                        style={styles.emptyButton}
                    >
                        Report Your First Reaction
                    </Button>
                </View>
            );
        }

        return (
            <View style={styles.reactionItem}>
                <View style={styles.reactionHeader}>
                    <Text style={styles.drugName}>{recentReport.drugName}</Text>
                    <Text
                        style={[styles.status, { color: getStatusColor(recentReport.status) }]}
                    >
                        {recentReport.status}
                    </Text>
                </View>
                <Text style={styles.reactionDetails}>
                    {recentReport.reactionDetails}
                </Text>
                <Text style={styles.date}>
                    {new Date(recentReport.dateReported).toLocaleDateString()}
                </Text>
            </View>
        );
    };

    return (
        <ScrollView style={styles.container}>
            <View style={styles.header}>
                <View>
                    <Text style={styles.title}>Patient Dashboard</Text>
                    <Text style={styles.subtitle}>Welcome, {user?.name}</Text>
                </View>
                <IconButton
                    icon="logout"
                    size={24}
                    iconColor={theme.colors.primary}
                    onPress={showLogoutDialog}
                />
            </View>

            <Card style={styles.quickActionsCard}>
                <Card.Content>
                    <Text style={styles.cardTitle}>Quick Actions</Text>
                    <Button
                        mode="contained"
                        icon="alert-circle"
                        onPress={() => navigation.navigate('ReportReaction')}
                        style={styles.button}
                        buttonColor={paperTheme.colors.primary}
                    >
                        Report Drug Reaction
                    </Button>
                    <Button
                        mode="outlined"
                        icon="chat"
                        onPress={() => navigation.navigate('PatientChat')}
                        style={styles.button}
                    >
                        Chat with Counsellor
                    </Button>
                    <Button
                        mode="outlined"
                        icon="eye"
                        onPress={() => navigation.navigate('ViewReactions')}
                        style={styles.button}
                    >
                        View All Reactions
                    </Button>
                </Card.Content>
            </Card>

            {renderStats()}

            <Card style={styles.recentReportsCard}>
                <Card.Content>
                    <Text style={styles.cardTitle}>Recent Report</Text>
                    {renderRecentReport()}
                </Card.Content>
            </Card>

            <Card style={[styles.profileCard, styles.lastCard]}>
                <Card.Content>
                    <Text style={styles.cardTitle}>Profile Information</Text>
                    <Button
                        mode="outlined"
                        icon="account-edit"
                        onPress={() => navigation.navigate('EditProfile')}
                        style={styles.button}
                    >
                        Edit Profile
                    </Button>
                </Card.Content>
            </Card>

            {/* Logout Confirmation Dialog */}
            <Portal>
                <Dialog
                    visible={logoutDialogVisible}
                    onDismiss={hideLogoutDialog}
                    style={[styles.dialogContainer, styles.responsiveDialog]}
                >
                    <View style={styles.iconContainer}>
                        <Avatar.Icon
                            size={screenWidth < SCREEN_BREAKPOINT_TABLET ? 56 : 64}
                            icon="logout"
                            color="white"
                            style={{ backgroundColor: paperTheme.colors.error }}
                        />
                    </View>
                    <Dialog.Title style={[
                        styles.dialogTitle,
                        screenWidth < SCREEN_BREAKPOINT_TABLET && styles.dialogTitleSmall
                    ]}>
                        Confirm Logout
                    </Dialog.Title>
                    <Dialog.Content style={styles.dialogContent}>
                        <Paragraph style={[
                            styles.dialogMessage,
                            screenWidth < SCREEN_BREAKPOINT_TABLET && styles.dialogMessageSmall
                        ]}>
                            Are you sure you want to log out of the Adverse Drug Reaction Reporting System?
                        </Paragraph>
                    </Dialog.Content>
                    <Dialog.Actions style={[
                        styles.dialogActions,
                        screenWidth < SCREEN_BREAKPOINT_TABLET && styles.dialogActionsSmall
                    ]}>
                        <Button
                            onPress={hideLogoutDialog}
                            style={[
                                styles.cancelButton,
                                screenWidth < SCREEN_BREAKPOINT_TABLET && styles.buttonSmall
                            ]}
                            labelStyle={[
                                { color: paperTheme.colors.onSurface },
                                screenWidth < SCREEN_BREAKPOINT_TABLET && styles.buttonLabelSmall
                            ]}
                            mode="outlined"
                        >
                            Cancel
                        </Button>
                        <Button
                            onPress={handleLogoutConfirm}
                            mode="contained"
                            style={[
                                styles.logoutButton,
                                screenWidth < SCREEN_BREAKPOINT_TABLET && styles.buttonSmall
                            ]}
                            labelStyle={[
                                { color: 'white' },
                                screenWidth < SCREEN_BREAKPOINT_TABLET && styles.buttonLabelSmall
                            ]}
                        >
                            Log Out
                        </Button>
                    </Dialog.Actions>
                </Dialog>
            </Portal>
        </ScrollView>
    );
};
// Create styles using the theme and screen width
const createStyles = (theme: any, screenWidth: number) => StyleSheet.create({
    container: {
        ...globalStyles.container,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: theme.spacing.lg,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        color: theme.colors.primary,
    },
    subtitle: {
        fontSize: 16,
        color: theme.colors.text,
        marginTop: theme.spacing.xs,
    },
    quickActionsCard: {
        ...globalStyles.card,
        marginBottom: theme.spacing.md,
    },
    statsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: theme.spacing.md,
    },
    statCard: {
        flex: 1,
        margin: theme.spacing.xs,
        padding: theme.spacing.sm,
        alignItems: 'center',
        borderRadius: theme.roundness,
        elevation: 2,
        backgroundColor: theme.colors.surface,
    },
    statNumber: {
        fontSize: 24,
        fontWeight: 'bold',
        color: theme.colors.primary,
    },
    statLabel: {
        fontSize: 12,
        color: theme.colors.text,
        textAlign: 'center',
        marginTop: theme.spacing.xs,
    },
    recentReportsCard: {
        ...globalStyles.card,
        marginBottom: theme.spacing.md,
    },
    profileCard: {
        ...globalStyles.card,
    },
    lastCard: {
        marginBottom: theme.spacing.xl,
    },
    cardTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: theme.colors.text,
        marginBottom: theme.spacing.md,
    },
    button: {
        marginVertical: theme.spacing.xs,
    },
    reactionItem: {
        marginBottom: theme.spacing.md,
        padding: theme.spacing.sm,
        backgroundColor: theme.colors.background,
        borderRadius: theme.roundness,
    },
    reactionHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: theme.spacing.xs,
    },
    drugName: {
        fontSize: 16,
        fontWeight: 'bold',
        color: theme.colors.text,
    },
    reactionDetails: {
        fontSize: 14,
        color: theme.colors.text,
        marginBottom: theme.spacing.xs,
    },
    reactionDescription: {
        fontSize: 14,
        color: theme.colors.text,
        marginBottom: theme.spacing.xs,
    },
    status: {
        fontSize: 14,
        fontWeight: 'bold',
    },
    date: {
        fontSize: 12,
        color: theme.colors.placeholder,
    },
    loadingContainer: {
        padding: theme.spacing.lg,
        alignItems: 'center',
        justifyContent: 'center',
    },
    emptyContainer: {
        padding: theme.spacing.lg,
        alignItems: 'center',
        justifyContent: 'center',
    },
    emptyText: {
        textAlign: 'center',
        color: theme.colors.placeholder,
        fontSize: 16,
        marginVertical: theme.spacing.md,
    },
    emptyButton: {
        marginTop: theme.spacing.md,
    },
    // Logout dialog styles
    dialogContainer: {
        borderRadius: theme.roundness * 2,
        backgroundColor: theme.colors.surface,
        ...globalStyles.shadow,
    },
    dialogTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: theme.colors.primary,
        textAlign: 'center',
        marginBottom: theme.spacing.sm,
    },
    dialogContent: {
        paddingHorizontal: theme.spacing.lg,
        paddingBottom: theme.spacing.md,
    },
    dialogMessage: {
        fontSize: 16,
        color: theme.colors.text,
        textAlign: 'center',
        marginBottom: theme.spacing.md,
    },
    dialogActions: {
        marginTop: theme.spacing.sm,
        paddingHorizontal: theme.spacing.md,
        paddingBottom: theme.spacing.md,
        justifyContent: 'space-between',
    },
    cancelButton: {
        marginRight: theme.spacing.md,
    },
    logoutButton: {
        backgroundColor: theme.colors.error,
    },
    iconContainer: {
        alignItems: 'center',
        marginBottom: theme.spacing.md,
    },
    responsiveDialog: {
        width: screenWidth > 500 ? 400 : '85%',
        alignSelf: 'center',
    },
    // Responsive styles for small screens
    dialogTitleSmall: {
        fontSize: 18,
        marginBottom: theme.spacing.xs,
    },
    dialogMessageSmall: {
        fontSize: 14,
        marginBottom: theme.spacing.sm,
    },
    dialogActionsSmall: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: theme.spacing.sm,
        paddingBottom: theme.spacing.sm,
    },
    buttonLabelSmall: {
        fontSize: 12,
    },
    buttonSmall: {
        paddingHorizontal: theme.spacing.sm,
        minWidth: 80,
    },
});
