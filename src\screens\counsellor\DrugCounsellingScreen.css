/* Table Title */
.table-title {
    color: #2c3e50;
    font-size: 24px;
    font-weight: bold;
    margin-left: 25px;
    display: flex;
    align-items: center;
    gap: 15px; /* Space between title and icons */
}

/* Body Scroll Fix */
body {
    overflow-y: auto !important;
    overflow-x: auto !important;
}

/* 🔹 Table Header - Flexbox Fix */
.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    flex-wrap: wrap;
    position: relative;
}

/* 🔹 Action Buttons - Stick Near Table Title */
.action-buttons-container {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-left: 10px; /* Ensures buttons stick next to the title */
}

/* 🔹 Icons for Edit, Delete, Export */
.icon-button {
    font-size: 20px;
    cursor: pointer;
    transition: transform 0.2s ease-in-out;
}

.icon-button:hover {
    transform: scale(1.1);
}

/* 🔹 Search Input - Fixed Position on Right */
.search-input-container {
    display: flex;
    justify-content: flex-end;
    width: 250px; /* Fixed width */
}

/* 🔹 Search Input Field */
.search-input {
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    width: 100%;
    margin-right: 50px;
}

/* 🔹 Checkbox Alignment */
.checkbox-cell {
    text-align: center;
}

/* 🔹 RESPONSIVE DESIGN FOR MOBILE 📱 */

/* 🔹 Adjust layout for tablets and small desktops (below 1024px) */
@media (max-width: 1024px) {
    .table-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .search-input-container {
        width: 100%;
        margin-right: 0px;
    }
    
}

/* 🔹 Adjust layout for mobile (below 768px) */
@media (max-width: 768px) {
    .table-header {
        flex-direction: column;
        align-items: center;
        gap: 10px; /* Space between title, icons, and search input */
    }

    .table-title {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .action-buttons-container {
        margin-left: 0;
        gap: 15px;
    }

    .icon-button {
        font-size: 18px;
    }

    .search-input-container {
        width: 90%;
        justify-content: center;
        margin-top: 10px;
    }
}

/* 🔹 Optimize for extra small mobile screens (below 480px) */
@media (max-width: 480px) {
    .table-title {
        font-size: 20px;
    }

    .action-buttons-container {
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
    }

    .icon-button {
        font-size: 16px;
    }

    .search-input {
        width: 100%;
        margin-right: 0px;
    }
}
