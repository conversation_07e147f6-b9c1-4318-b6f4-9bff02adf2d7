import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { TextInput, Button, Text, ActivityIndicator } from 'react-native-paper';
import { theme, styles as globalStyles } from '../../theme';
import { useAuth } from '../../context/AuthContext';
import { UpdateRegisterData } from '../../types';
import { Dropdown } from 'react-native-element-dropdown';
import { DatePickerModal } from 'react-native-paper-dates';

const sexOptions = [
  { label: 'Male', value: 'Male' },
  { label: 'Female', value: 'Female' },
  { label: 'Other', value: 'Other' },
];

const pregnantOptions = [
  { label: 'Yes', value: 'Yes' },
  { label: 'No', value: 'No' },
];

const counselOptions = [
  { label: 'Yes', value: 'Yes' },
  { label: 'No', value: 'No' },
];

const ethnicityOptions = [
  { label: 'Brahmin/Chhetri', value: '<PERSON>rah<PERSON>/<PERSON>tri' },
  { label: 'Terai/Madhesi', value: 'Terai/Madhesi' },
  { label: 'Dalits', value: 'Dalits' },
  { label: 'Newar', value: 'Newar' },
  { label: 'Janajati', value: 'Janajati' },
  { label: 'Muslim', value: 'Muslim' },
  { label: 'Other', value: 'Other' },
];

export const EditCredentialsScreen = ({ navigation }: any) => {
  const { updateRegistrationdata, isLoading, error, user } = useAuth();

  if (!user) return null;

  const [formData, setFormData] = useState<UpdateRegisterData>({
    email: user.email,
    username: user.username || '',
    hospitalNo: user.hospitalNo,
    firstName: user.firstName,
    lastName: user.lastName,
    age: user.age,
    sex: user.sex,
    weight: user.weight,
    contactNumber: user.contactNumber,
    ethnicity: user.ethnicity,
    pregnant: user.pregnant,
    dateOfBirth: user.dateOfBirth,
    priorCounsel: user.priorCounsel,
    role: user.role,
  });

  const [formError, setFormError] = useState<string>('');
  const [showDatePicker, setShowDatePicker] = useState(false);

  // Function to calculate age
  const calculateAge = (birthDate: Date): number => {
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  // Function to handle the date change
  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      // Convert date to ISO date string (YYYY-MM-DD)
      const isoDate = new Date(date.getTime() - date.getTimezoneOffset() * 60000)
        .toISOString()
        .split('T')[0];
      // Calculate age from the selected date
      const age = calculateAge(date);
      setFormData({ ...formData, dateOfBirth: isoDate, age });
    }
    setShowDatePicker(false);
  };

  // Save changes handler
  const handleSaveChanges = async () => {
    try {
      setFormError('');
      const updatedData = {
        // No need to include _id as we're standardizing on id
        id: user.id,
        name: user.name,
        createdAt: user.createdAt || '',
        status: user.status || 'active',
        designation: user.designation,
        department: user.department,
        email: formData.email || user.email,
        username: formData.username || user.username,
        hospitalNo: formData.hospitalNo || user.hospitalNo,
        firstName: formData.firstName || user.firstName,
        lastName: formData.lastName || user.lastName,
        age: formData.age || user.age,
        sex: formData.sex || user.sex,
        weight: formData.weight || user.weight,
        contactNumber: formData.contactNumber || user.contactNumber,
        phoneNumber: user.phoneNumber,
        ethnicity: formData.ethnicity || user.ethnicity,
        pregnant: formData.pregnant || user.pregnant,
        dateOfBirth: formData.dateOfBirth || user.dateOfBirth,
        priorCounsel: formData.priorCounsel || user.priorCounsel,
        role: formData.role || user.role,
      };

      await updateRegistrationdata(updatedData);
      navigation.goBack();
    } catch (err) {
      setFormError(err instanceof Error ? err.message : 'Update failed');
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  return (
    <ScrollView contentContainerStyle={styles.scrollContainer} keyboardShouldPersistTaps="handled">
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Edit Credentials</Text>
          <Text style={styles.subtitle}>Update your details</Text>
        </View>

        {(formError || error) && <Text style={styles.error}>{formError || error}</Text>}

        {/* TextInput Fields */}
        <TextInput label="First Name" value={formData.firstName} onChangeText={(text) => setFormData({ ...formData, firstName: text })} style={styles.input} mode="outlined" activeOutlineColor={theme.colors.primary} />
        <TextInput label="Last Name" value={formData.lastName} onChangeText={(text) => setFormData({ ...formData, lastName: text })} style={styles.input} mode="outlined" activeOutlineColor={theme.colors.primary} />
        <TextInput label="Username" value={formData.username} onChangeText={(text) => setFormData({ ...formData, username: text })} style={styles.input} mode="outlined" activeOutlineColor={theme.colors.primary} />
        <TextInput label="Email Address" value={formData.email} onChangeText={(text) => setFormData({ ...formData, email: text })} style={styles.input} mode="outlined" keyboardType="email-address" autoCapitalize="none" activeOutlineColor={theme.colors.primary} />
        <TextInput label="Hospital Number" value={formData.hospitalNo} onChangeText={(text) => setFormData({ ...formData, hospitalNo: text })} style={styles.input} mode="outlined" activeOutlineColor={theme.colors.primary} />

        {/* Date of Birth */}
        <TextInput
          label="Date of Birth"
          value={formData.dateOfBirth ? new Date(formData.dateOfBirth).toLocaleDateString('en-US') : ''}
          onFocus={() => setShowDatePicker(true)}
          style={styles.input}
          mode="outlined"
          activeOutlineColor={theme.colors.primary}
          left={<TextInput.Icon icon="calendar" color={theme.colors.primary} />}

        />

        {/* DatePicker Modal */}
        <DatePickerModal
          mode="single"
          visible={showDatePicker}
          onDismiss={() => setShowDatePicker(false)}
          date={formData.dateOfBirth ? new Date(formData.dateOfBirth) : new Date()}
          onConfirm={({ date }) => handleDateChange(date)}
          locale="en"
        />

        <TextInput label="Age"
          value={String(formData.age)}
          onChangeText={(text) => setFormData({ ...formData, age: Number(text) })}
          style={styles.input} mode="outlined"
          keyboardType="numeric"
          activeOutlineColor={theme.colors.primary}
          editable={false}
        />

        {/* Sex Dropdown */}
        <Dropdown
          style={styles.dropdown}
          data={sexOptions}
          labelField="label"
          valueField="value"
          placeholder="Select Sex"
          value={formData.sex}
          onChange={(item) => {
            const newFormData = { ...formData, sex: item.value };
            if (item.value === 'Male') {
              newFormData.pregnant = 'Not Applicable';
            }
            setFormData(newFormData);
          }}
        />

        <TextInput label="Weight" value={String(formData.weight)} onChangeText={(text) => setFormData({ ...formData, weight: Number(text) })} style={styles.input} mode="outlined" keyboardType="numeric" activeOutlineColor={theme.colors.primary} />
        <TextInput label="Contact Number" value={formData.contactNumber} onChangeText={(text) => setFormData({ ...formData, contactNumber: text })} style={styles.input} mode="outlined" keyboardType="phone-pad" activeOutlineColor={theme.colors.primary} />
        {/* Ethnicity Dropdown */}
        <Dropdown
          style={styles.dropdown}
          data={ethnicityOptions}
          labelField="label"
          valueField="value"
          placeholder="Select Ethnicity"
          value={formData.ethnicity}
          onChange={(item) => {
            setFormData({ ...formData, ethnicity: item.value });
          }}
        />

{/* Conditionally render Pregnant Dropdown for non-male users */}
{formData.sex !== 'Male' && (
  <>
    <Text style={styles.dropdownLabel}>Pregnancy Status</Text>
    <Dropdown
      style={styles.dropdown}
      data={pregnantOptions}
      labelField="label"
      valueField="value"
      placeholder="Are you pregnant?"
      value={formData.pregnant}
      onChange={(item) => setFormData({ ...formData, pregnant: item.value })}
    />
  </>
)}


        {/* Prior Counseling Dropdown */}
        <Text style={styles.label}>Prior Counseling (Yes/No)</Text>
        <Dropdown
          style={styles.dropdown}
          data={counselOptions}
          labelField="label"
          valueField="value"
          placeholder="Select option"
          value={formData.priorCounsel}
          onChange={(item) => setFormData({ ...formData, priorCounsel: item.value })}
        />

        {/* Save Changes Button */}
        <Button mode="contained" onPress={handleSaveChanges} style={globalStyles.button}>
          Save Changes
        </Button>
        <Button mode="text" onPress={() => navigation.goBack()} style={styles.linkButton} textColor={theme.colors.primary}>Cancel</Button>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollContainer: { flexGrow: 1 },
  container: { ...globalStyles.container, justifyContent: 'center' },
  loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: theme.colors.background },
  header: { alignItems: 'center', marginBottom: theme.spacing.lg },
  title: { fontSize: 24, fontWeight: 'bold', color: theme.colors.primary },
  subtitle: { fontSize: 16, color: theme.colors.text, marginTop: theme.spacing.xs },
  input: { ...globalStyles.input, backgroundColor: theme.colors.surface },
  dropdown: { height: 50, borderColor: '#ccc', borderWidth: 1, borderRadius: 5, paddingHorizontal: 10, marginBottom: 10, justifyContent: 'center' },
  button: { marginVertical: 10, padding: 10 },
  linkButton: { marginVertical: 10, padding: 10 },
  error: { color: 'red', marginBottom: 10, textAlign: 'center' },
  dropdownLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
    marginLeft: 4,
  },

  label: { fontSize: 14, color: theme.colors.text, marginBottom: 5 },
});
