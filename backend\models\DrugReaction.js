const mongoose = require('mongoose');

const drugReactionSchema = new mongoose.Schema({
    user: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
        ref: 'User'
    },
    drugName: {
        type: String,
        required: true
    },
    reactionDetails: {
        type: String,
        required: true
    },
    symptoms: {
        type: [String],
        required: true
    },
    severity: {
        type: String,
        enum: ['mild', 'moderate', 'severe'],
        required: true
    },
    status: {
        type: String,
        enum: ['pending', 'resolved'],
        default: 'pending'
    },
    resolvedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: false
    },
    resolvedAt: {
        type: Date,
        required: false
    },
    dateReported: {
        type: Date,
        default: Date.now
    },
    assignedCounsellorId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    assignedCounsellorName: String,
    treatmentGiven: String,
    image: String,
    indications: {
        type: String,
    },
    dateStopped: {
        type: Date,
    },
    dateStarted: {
        type: Date,
    },
    dailyDose: {
        type: String,
    },
    routeOfAdministration: {
        type: String,
    },
    dosageForm: {
        type: String,
    },
    updatedAt: {
        type: Date,
        default: Date.now
    },
    hospitalized: { 
        type: Boolean,
        default: false
    },
    allergyCard: { 
        type: Boolean,
        default: false
    },
    discontinuation: { 
        type: Boolean,
        default: false
    },
    timeAfterExposure: { 
        type: String,
    },
    outcomesOthers: {
        type: String,
    },
    outcomes: { 
        type: [String], 
        enum: ['Fatal', 'Recovered', 'Recovering', 'Continuing', 'Unknown', 'Others'], 
        default: [], 
    },
    createdAt: {
        type: Date,
        default: Date.now,
        required: true
    },
    modifiedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: false
    },
    modifiedAt: {
        type: Date,
        default: Date.now,
        required: false
    }
});

// Update the modifiedAt field before saving or updating the document
drugReactionSchema.pre('save', function(next) {
    this.modifiedAt = new Date();
    if (!this.createdAt) {
        this.createdAt = this.modifiedAt; // Set createdAt if it's not already set
    }
    next();
});

drugReactionSchema.pre('findOneAndUpdate', function(next) {
    this._update.modifiedAt = new Date();
    next();
});

module.exports = mongoose.model('DrugReaction', drugReactionSchema);
