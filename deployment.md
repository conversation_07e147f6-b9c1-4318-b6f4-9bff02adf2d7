# Deployment Guide for Drug Counselling App

This guide provides a step-by-step process for deploying the Drug Counselling App on an AWS Lightsail Ubuntu server, including backend (Node.js + Express + MongoDB) and frontend (React Native Web with Expo) setup, as well as Nginx configuration as a reverse proxy.

## 1. Connect to Your AWS Lightsail Server

Use SSH to connect to your server:

```bash
ssh ubuntu@***********
```

## 2. Install Required Software on Ubuntu

Update the system and install necessary packages:

```bash
# Update Ubuntu packages
sudo apt update && sudo apt upgrade -y
```
# Install Node.js (latest LTS version)
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E 
sudo apt install -y nodejs npm
```
# Install MongoDB (optional if using local DB)
```bash
sudo apt-get install gnupg curl
curl -fsSL https://www.mongodb.org/static/pgp/server-8.0.asc | sudo gpg -o /usr/share/keyrings/mongodb-server-8.0.gpg --dearmor
echo "deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-8.0.gpg ] https://repo.mongodb.org/apt/ubuntu noble/mongodb-org/8.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-8.0.list
sudo apt-get update
sudo apt-get install -y mongodb-org
sudo systemctl start mongod
sudo systemctl enable mongod
sudo systemctl status mongod
```
# Install PM2 (to keep backend running)
```bash
sudo npm install -g pm2
```
# Install Nginx (for reverse proxy)
```bash
sudo apt install -y nginx
```

## 3. Setup Backend

### Transfer Backend Code to Server

If your backend code is on your local machine, transfer it to the server:

```bash
scp -r /path/to/backend ubuntu@***********:/home/<USER>/
```

Or, clone from GitHub:

```bash
git clone https://github.com/your-repo/backend.git
```

### Install Backend Dependencies

```bash
cd backend
npm install
```

### Configure Environment Variables

Create a `.env` file:

```bash
nano .env
```

Add the following:

```
PORT=5000
MONGO_URI=mongodb://localhost:27017/drugcounselling
JWT_SECRET=your-secret-key
```

Save and exit (Press CTRL + X, then Y, then Enter).

### Start Backend with PM2

Run the backend server in production mode:

```bash
pm2 start index.js --name backend
pm2 save
pm2 startup
```

## 4. Setup Frontend

### Transfer Frontend Code to Server

If your frontend is on your local machine:

```bash
scp -r /path/to/frontend ubuntu@***********:/home/<USER>/
```

Or, clone it from GitHub:

```bash
git clone https://github.com/your-repo/frontend.git
```

### Install Frontend Dependencies

```bash
cd frontend
npm install --legacy-peer-deps
```

### Build Web Version
```bash
npx expo export
```
Ensure the files are in the correct location:

```bash
sudo mkdir -p /var/www/demo.com
sudo cp -r dist/* /var/www/demo.com
```

## 5. Configure Nginx as a Reverse Proxy

### Create Nginx Configuration

```bash
sudo nano /etc/nginx/sites-available/demo.com
```
## Add the following configuration:

```bash
server {
    listen 80;
    server_name demo.com www.demo.com;

    location / {
        root /var/www/demo.com;
        index index.html;
        try_files $uri /index.html;
    }

    location /api/ {
        proxy_pass http://localhost:5000/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## Save and exit (CTRL + X, then Y, then Enter).

### Enable the Configuration and Restart Nginx

```bash
sudo ln -s /etc/nginx/sites-available/demo.com /etc/nginx/sites-enabled/
sudo nginx -t 
sudo systemctl restart nginx
```

## 6. Secure Your Website with SSL (HTTPS)

### Install Certbot for SSL

```bash
sudo apt install -y certbot python3-certbot-nginx
```

### Generate SSL Certificate

```bash
sudo certbot --nginx -d demossfd.com -d www.demossfd.com
```

Follow the prompts to generate and install SSL certificates.

### Auto-Renew SSL Certificate

Add a cron job to renew SSL automatically:

```bash
sudo crontab -e
```

Add this line:

```bash
0 0 1 * * certbot renew --quiet
```

Save and exit (CTRL + X, then Y, then Enter).

## 7. Final Testing & Deployment

### Restart All Services

```bash
sudo systemctl restart nginx
pm2 restart backend
```

### Test Deployment

- Visit `http://demo.com` → Your frontend should load.
- Visit `http://demo.com/api/` → Your backend API should work.
- Visit `https://demo.com` → SSL should be enabled.

## 8. Additional Configurations


## Frontend (React Native App) Configuration

1.  **Update API Endpoint Environment Variable:**
    *   Your code likely uses `process.env.EXPO_PUBLIC_API_URL` (e.g., in `src/context/AuthContext.tsx`).
    *   **Do not change the code itself.** Instead, configure the value of this environment variable for your production build.
    *   **If using Expo EAS Build:** Configure `EXPO_PUBLIC_API_URL` in your `eas.json` build profile or via EAS Secrets. Set its value to `https://api.demo.com`.
    *   **For Local Development:** You can use a `.env` file in the root of your React Native project (ensure it's in `.gitignore`) with `EXPO_PUBLIC_API_URL=http://<your-local-ip>:5000` or `http://127.0.0.1:5000`.      //////// "https://demo.com/api"
2.  **Update Socket.IO Endpoint:**
    *   Find where the Socket.IO client connection is established (likely near where `API_BASE_URL` is used or in a dedicated chat/socket service file).
    *   Ensure it also uses an environment variable or is updated to connect to `https://api.demo.com` for production builds. If it's hardcoded, consider changing it to use `process.env.EXPO_PUBLIC_API_URL` as well.
        ```javascript
        // Example: Ensure this points to the production URL for builds
        import io from 'socket.io-client';
        const socket = io(process.env.EXPO_PUBLIC_API_URL || 'http://127.0.0.1:5000');
        ```
### change this line to this in src/context/AuthContext.tsx
        ```
        export const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL || 'https://adr.copilot-ocean.com/api';
        ```
### PM2 Monitoring

Check backend logs:

```bash
pm2 logs drug-backend
```

Restart backend if needed:

```bash
pm2 restart backend
```

### Auto-start PM2 on Reboot

```bash
pm2 save
pm2 startup
```

## 🎯 Summary of Steps

| Step                     | Description                                           |
|--------------------------|-------------------------------------------------------|
| 1. Connect to Server     | SSH into the AWS Lightsail instance.                 |
| 2. Install Dependencies   | Install Node.js, MongoDB, PM2, and Nginx.           |
| 3. Setup Backend         | Upload backend, install dependencies, start with PM2.|
| 4. Setup Frontend        | Upload frontend, build the web version, copy files to /var/www/demo.com. |
| 5. Configure Nginx      | Set up Nginx as a reverse proxy for both frontend and API. |
| 6. Enable SSL           | Install and configure Let's Encrypt for HTTPS.       |
| 7. Restart Services      | Ensure all services are running.                     |
| 8. Final Testing        | Test frontend, API, and SSL certificate.             |

## 🎉 Congratulations!

Your Drug Counselling App is now deployed on AWS Lightsail with a production setup! 🚀🎯
If anything doesn’t work, check:

- `pm2 logs drug-backend` for backend errors.
- `sudo systemctl status nginx` for Nginx issues.
- `certbot renew --dry-run` to test SSL renewal.

Building apk / abb file
```bash
npm install -g eas-cli
eas build:configure
eas build --platform android

# eas.json

## abb File
```bash
{
 "cli": {
   "version": ">= 16.6.1",
   "appVersionSource": "remote"
 },
 "build": {
   "development": {
     "developmentClient": true,
     "distribution": "internal"
   },
   "preview": {
     "distribution": "internal"
   },
   "production": {
     "autoIncrement": true
   }
 },
 "submit": {
   "production": {}
 }
}
```
Apk File
```bash
{
  "cli": {
    "version": ">= 15.0.15",
    "appVersionSource": "remote"
  },
  "build": {
    "release": {
      "android": {
        "buildType": "apk"
      }
    }
  }
}
```

```bash
eas build --platform android --profile release
```