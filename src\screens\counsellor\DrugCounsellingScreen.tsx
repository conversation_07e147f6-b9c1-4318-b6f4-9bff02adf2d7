import React, { useState, useContext, useEffect } from 'react';
import { DrugCounsellingContext } from '../../context/DrugCounsellingContext';
import ExcelJS from 'exceljs';
import DrugCounsellingForm from '../../components/DrugCounsellingForm';
import CounsellingDataTable from '../../components/CounsellingDataTable';
import { CounsellingData } from '../../types/counsellingData';
import { FaEdit, FaTrash, FaFileExport } from 'react-icons/fa';
import './DrugCounsellingScreen.css';
import { useAuth, API_BASE_URL } from '../../context/AuthContext';
import { useFocusEffect } from '@react-navigation/native'; // Import useFocusEffect

const DrugCounsellingScreen = () => {
  const context = useContext(DrugCounsellingContext);
  if (!context) {
    throw new Error('DrugCounsellingScreen must be used within a DrugCounsellingProvider');
  }

  const {
    counsellingData,
    addCounsellingData,
    clearCounsellingData,
    deleteCounsellingData,
    updateCounsellingData,
  } = context;

  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedRows, setSelectedRows] = useState<string[]>([]);
  const [editingData, setEditingData] = useState<CounsellingData | null>(null);

  const fetchCounsellingData = async () => {
    try {
      clearCounsellingData();
      const response = await fetch(`${API_BASE_URL}/api/drugCounselling`);
      if (!response.ok) throw new Error('Failed to fetch data from server');
      const data = await response.json();
      data.forEach((item: CounsellingData) => addCounsellingData(item));
    } catch (err: any) {
      setError(err.message || 'Error fetching data');
    } finally {
      setLoading(false);
    }
  };

  // Use `useFocusEffect` to reload data when the screen is focused
  useFocusEffect(
    React.useCallback(() => {
      fetchCounsellingData();
    }, [])
  );

  const handleRowSelection = (item: CounsellingData) => {
    const id = item._id?.toString();
    if (!id) return;
    setSelectedRows((prev) =>
      prev.includes(id) ? prev.filter((rowId) => rowId !== id) : [...prev, id]
    );
  };

  const handleDelete = async () => {
    try {
      await Promise.all(
        selectedRows.map(async (id) => {
          const res = await fetch(`${API_BASE_URL}/api/drugCounselling/${id}`, {
            method: 'DELETE',
          });
          if (!res.ok) throw new Error('Failed to delete entry');
          deleteCounsellingData(id);
        })
      );
      setSelectedRows([]);
    } catch (err: any) {
      setError('Failed to delete selected rows.');
    }
  };

  const handleEdit = () => {
    if (selectedRows.length !== 1) {
      alert('Please select exactly one row to edit.');
      return;
    }
    const entry = counsellingData.find((item) => item._id?.toString() === selectedRows[0]);
    if (entry) setEditingData(entry);
  };

  const handleSaveEdit = async (updated: CounsellingData) => {
    try {
      setLoading(true);
      const res = await fetch(`${API_BASE_URL}/api/drugCounselling/${updated._id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updated),
      });
      if (!res.ok) throw new Error('Failed to update');
      updateCounsellingData(updated);
      setEditingData(null);
      setSelectedRows([]);
    } catch (err: any) {
      setError('Failed to update entry.');
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async () => {
    try {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('CounsellingData');

      // Define columns
      worksheet.columns = [
        { header: 'Date', key: 'date', width: 15 },
        { header: 'Hospital No', key: 'hospitalNo', width: 15 },
        { header: 'Patient Name', key: 'patientName', width: 20 },
        { header: 'Age/Sex', key: 'ageSex', width: 10 },
        { header: 'Address', key: 'address', width: 20 },
        { header: 'Contact No', key: 'contactNo', width: 15 },
        { header: 'Diagnosis', key: 'diagnosis', width: 20 },
        { header: 'Medical History', key: 'medicalHistory', width: 20 },
        { header: 'Medication History', key: 'medicationHistory', width: 20 },
        { header: 'Allergy History', key: 'allergyHistory', width: 20 },
        { header: 'Alcohol Intake', key: 'alcoholIntake', width: 15 },
        { header: 'Smoking History', key: 'smokingHistory', width: 15 },
        { header: 'Prescribed Medicine', key: 'prescribedMedicine', width: 20 },
        { header: 'Counseling Provided', key: 'counselingProvided', width: 20 },
        { header: 'Average Counseling Time', key: 'averageCounselingTime', width: 15 },
        { header: 'Department', key: 'department', width: 15 },
        { header: 'Created By', key: 'createdBy', width: 15 },
      ];

      // Add rows
      counsellingData.forEach((item) => {
        worksheet.addRow({
          ...item,
          date: item.date ? new Date(item.date).toLocaleDateString() : '',
        });
      });

      // Create buffer and trigger download
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'counselling_data.xlsx';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      setError('Failed to export data.');
    }
  };

  const filteredData = counsellingData.filter((item) =>
    Object.values(item).some((value) =>
      value?.toString().toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  if (loading) return <div>Loading...</div>;
  if (error) return <div className="error-message">Error: {error}</div>;

  return (
    <div>
      <DrugCounsellingForm
        existingData={editingData}
        onSave={handleSaveEdit}
        onCancel={() => setEditingData(null)}
      />

      <div className="table-header">
        <div className="table-title">
          Counselling Records
          <div className="action-buttons-container">
            {selectedRows.length > 0 && (
              <>
                <FaEdit className="icon-button" onClick={handleEdit} title="Edit selected" aria-hidden="true" />
                <FaTrash className="icon-button" onClick={handleDelete} title="Delete selected" aria-hidden="true" />
              </>
            )}
            <FaFileExport className="icon-button" onClick={handleExport} title="Export to Excel" aria-hidden="true" />
          </div>
          <div className="search-input-container">
            <input
              type="text"
              placeholder="Search records..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="search-input"
            />
          </div>
        </div>
      </div>

      <CounsellingDataTable
        data={filteredData}
        onRowSelect={handleRowSelection}
        selectedRows={selectedRows}
      />
    </div>
  );
};

export default DrugCounsellingScreen;
