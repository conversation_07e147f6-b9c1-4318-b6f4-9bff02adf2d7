const express = require('express');
const router = express.Router();
const DrugCounselling = require('../models/DrugCounselling');
const mongoose = require('mongoose');

// Create new Drug Counselling
router.post('/', async (req, res) => {
    try {
        console.log('Request body:', req.body); 
        const { createdBy, createdAt, modifiedBy, modifiedAt, ...counsellingData } = req.body; // Destructure to get new fields
        const newCounselling = new DrugCounselling({
            ...counsellingData,
            createdBy,
            createdAt,
            modifiedBy,
            modifiedAt
        });
        await newCounselling.save();
        res.status(201).json(newCounselling);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
});

// Get all Drug Counselling records
router.get('/', async (req, res) => {
    try {
        const counsellingData = await DrugCounselling.find();
        console.log(counsellingData);  // Log the fetched records
        res.status(200).json(counsellingData);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// Update an existing Drug Counselling record
router.put('/:id', async (req, res) => {
    try {
        const { modifiedBy, ...counsellingData } = req.body;

        // Validate the ID format before querying DB
        if (!mongoose.Types.ObjectId.isValid(req.params.id)) {
            return res.status(400).json({ message: 'Invalid counselling ID format' });
        }

        const updatedCounselling = await DrugCounselling.findByIdAndUpdate(
            req.params.id,
            {
                ...counsellingData,
                modifiedBy,
                modifiedAt: new Date(),
            },
            { new: true, runValidators: true } // runValidators ensures schema validation
        );

        if (!updatedCounselling) {
            return res.status(404).json({ message: 'Counselling record not found' });
        }

        res.status(200).json(updatedCounselling);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});


// Delete an existing Drug Counselling record
router.delete('/:id', async (req, res) => {
    try {
        const deletedCounselling = await DrugCounselling.findByIdAndDelete(req.params.id);
        if (!deletedCounselling) {
            return res.status(404).json({ message: 'Counselling record not found' });
        }
        res.status(200).json({ message: 'Counselling record deleted successfully' });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

module.exports = router;
