.icon-button {
    font-size: 20px;
    cursor: pointer;
    margin: 0 8px;
    transition: transform 0.2s ease-in-out;
}

.icon-button:hover {
    transform: scale(1.1);
}

.data-table-container {
    overflow-x: auto;
    width: 100%;
    border: 1px solid #ddd;
}

.counselling-data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    white-space: nowrap;
}

.counselling-data-table th, 
.counselling-data-table td {
    padding: 10px;
    border: 1px solid #ddd;
    text-align: left;
}

.counselling-data-table thead th {
    background-color: #f8f9fa;
    position: sticky;
    top: 0;
    z-index: 2;
}

.counselling-data-table tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

.counselling-data-table tbody tr:hover {
    background-color: #eef6ff;
}
