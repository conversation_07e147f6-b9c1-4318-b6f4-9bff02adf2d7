import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, FlatList, Text, TextInput, Dimensions, TouchableOpacity } from 'react-native';
import { useTheme, IconButton } from 'react-native-paper';
import axios from 'axios';
import { io } from 'socket.io-client';
import { useAuth, API_BASE_URL } from '../../context/AuthContext';
import ChatMessage from '../../components/ChatMessage';
import ChatInput from '../../components/ChatInput';
import { searchMessages, searchUsers } from '../../utils/searchUtils';
import { Message, Attachment } from '../../types/chat';

export const DoctorChatScreen: React.FC = () => {
  const theme = useTheme();
  const { user } = useAuth();

  // Messages and counsellor state
  const [messages, setMessages] = useState<Message[]>([]);
  const [socket, setSocket] = useState<any>(null);
  const [counsellors, setCounsellors] = useState<any[]>([]);
  const [selectedCounsellor, setSelectedCounsellor] = useState<string | null>(null);
  const [unreadMessages, setUnreadMessages] = useState<{ [key: string]: number }>({});

  // UI state
  const [isUserAtBottom, setIsUserAtBottom] = useState(true);
  const flatListRef = useRef<FlatList>(null);
  const screenWidth = Dimensions.get('window').width;
  const isMobile = screenWidth < 600;
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [counsellorSearchTerm, setCounsellorSearchTerm] = useState('');

  // Filter messages based on search using enhanced search utility
  const displayedMessages = searchMessages(messages, searchTerm);

  // Filter counsellors based on search using enhanced search utility
  const filteredCounsellors = searchUsers(counsellors, counsellorSearchTerm, ['email', 'specialization']);

  useEffect(() => {
    const newSocket = io(API_BASE_URL);
    setSocket(newSocket);

    const fetchCounsellors = async () => {
      try {
        const response = await axios.get(`${API_BASE_URL}/api/staff/counsellors`);
        setCounsellors(response.data);
      } catch (error) {
        console.error('Error fetching counsellors:', error);
      }
    };

    fetchCounsellors();

    return () => {
      if (newSocket) {
        newSocket.disconnect();
      }
    };
  }, []);

  useEffect(() => {
    if (!socket || !user?.id) return;

    socket.emit('join-room', user.id);

    socket.on('receive-message', (message: any) => {
      setMessages((prev) => {
        if (!prev.some((m) => m.id === message.id)) {
          return [...prev, message];
        }
        return prev;
      });

      // Update unread message count if message is from a counsellor
      if (message.receiverId === user.id && message.senderId !== selectedCounsellor) {
        setUnreadMessages((prev) => {
          const count = prev[message.senderId] || 0;
          return {
            ...prev,
            [message.senderId]: count + 1,
          };
        });
      }

      if (isUserAtBottom) {
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 100);
      }
    });

    return () => {
      socket.off('receive-message');
    };
  }, [socket, user, isUserAtBottom]);

  useEffect(() => {
    if (!selectedCounsellor || !user?.id) return;

    const fetchChatHistory = async () => {
      try {
        const response = await axios.get(
          `${API_BASE_URL}/api/chat/history/${user.id}/${selectedCounsellor}`
        );

        const formatted = response.data.map((msg: any) => ({
          id: msg._id,
          text: msg.content,
          timestamp: msg.timestamp,
          senderId: msg.senderId._id,
          senderName: msg.senderId.name,
          attachment: msg.attachment || null
        }));

        setMessages(formatted.reverse());

        // Reset unread count when selecting a counsellor
        setUnreadMessages((prev) => ({
          ...prev,
          [selectedCounsellor]: 0,
        }));

        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: false });
        }, 100);
      } catch (error) {
        console.error('Error fetching chat history:', error);
      }
    };

    fetchChatHistory();
  }, [selectedCounsellor, user]);

  const handleScroll = (event: any) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const atBottom = layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;
    setIsUserAtBottom(atBottom);
  };

  const handleSend = async (message: string, attachment?: Attachment) => {
    if (!selectedCounsellor || !user?.id || !socket) return;

    try {
      const messageData: any = {
        sender: user.id,
        receiver: selectedCounsellor,
        content: message
      };

      // Add attachment if present
      if (attachment) {
        messageData.attachment = attachment;
      }

      const response = await axios.post(`${API_BASE_URL}/api/chat/send`, messageData);

      socket.emit('send-message', {
        id: response.data._id,
        text: message,
        attachment: attachment || null,
        timestamp: new Date().toISOString(),
        senderId: user.id,
        senderName: user.name
      });
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {isMobile ? (
        selectedCounsellor ? (
          <View style={styles.fullWidthChatContainer}>
            <View style={styles.chatHeader}>
              <IconButton icon="arrow-left" onPress={() => setSelectedCounsellor(null)} size={24} />
              <Text style={styles.counsellorName}>
                {counsellors.find((c) => c._id === selectedCounsellor)?.name || 'Drug Counsellor'}
              </Text>
              <View style={styles.searchContainer}>
                {isSearchExpanded ? (
                  <View style={styles.searchInputContainer}>
                    <TextInput
                      style={styles.searchInput}
                      placeholder="Search messages..."
                      value={searchTerm}
                      onChangeText={setSearchTerm}
                      autoFocus
                    />
                    <IconButton
                      icon="close"
                      onPress={() => {
                        setSearchTerm('');
                        setIsSearchExpanded(false);
                      }}
                      size={20}
                    />
                  </View>
                ) : (
                  <IconButton
                    icon="magnify"
                    onPress={() => setIsSearchExpanded(true)}
                    size={24}
                  />
                )}
              </View>
            </View>

            <FlatList
              ref={flatListRef}
              data={displayedMessages}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <ChatMessage
                  message={item.text}
                  isCurrentUser={item.senderId === user?.id}
                  timestamp={item.timestamp}
                  senderName={item.senderName}
                  attachment={item.attachment}
                />
              )}
              contentContainerStyle={styles.messagesContainer}
              onScroll={handleScroll}
            />
            <ChatInput onSend={handleSend} />
          </View>
        ) : (
          <View style={styles.fullWidthCounsellorList}>
            <View style={styles.counsellorSearchContainer}>
              <TextInput
                style={styles.counsellorSearchInput}
                placeholder="Search counsellors..."
                value={counsellorSearchTerm}
                onChangeText={setCounsellorSearchTerm}
              />
            </View>

            <FlatList
              data={filteredCounsellors}
              keyExtractor={(item) => item._id}
              renderItem={({ item: counsellor }) => (
                <TouchableOpacity
                  style={[
                    styles.counsellorItem,
                    selectedCounsellor === counsellor._id && styles.selectedCounsellor
                  ]}
                  onPress={() => setSelectedCounsellor(counsellor._id)}
                >
                  <Text style={styles.counsellorName}>{counsellor.name}</Text>
                  {unreadMessages[counsellor._id] > 0 && (
                    <View style={styles.badge}>
                      <Text style={styles.badgeText}>{unreadMessages[counsellor._id]}</Text>
                    </View>
                  )}
                </TouchableOpacity>
              )}
              contentContainerStyle={styles.counsellorList}
            />
          </View>
        )
      ) : (
        <>
          <View style={styles.leftPanel}>
            <View style={styles.counsellorSearchContainer}>
              <TextInput
                style={styles.counsellorSearchInput}
                placeholder="Search counsellors..."
                value={counsellorSearchTerm}
                onChangeText={setCounsellorSearchTerm}
              />
            </View>

            <FlatList
              data={filteredCounsellors}
              keyExtractor={(item) => item._id}
              renderItem={({ item: counsellor }) => (
                <TouchableOpacity
                  style={[
                    styles.counsellorItem,
                    selectedCounsellor === counsellor._id && styles.selectedCounsellor
                  ]}
                  onPress={() => setSelectedCounsellor(counsellor._id)}
                >
                  <Text style={styles.counsellorName}>{counsellor.name}</Text>
                  {unreadMessages[counsellor._id] > 0 && (
                    <View style={styles.badge}>
                      <Text style={styles.badgeText}>{unreadMessages[counsellor._id]}</Text>
                    </View>
                  )}
                </TouchableOpacity>
              )}
              contentContainerStyle={styles.counsellorList}
            />
          </View>

          <View style={styles.rightPanel}>
            {selectedCounsellor ? (
              <>
                <View style={styles.chatHeader}>
                  <Text style={styles.counsellorName}>
                    {counsellors.find((c) => c._id === selectedCounsellor)?.name || 'Drug Counsellor'}
                  </Text>
                  <View style={styles.searchContainer}>
                    {isSearchExpanded ? (
                      <View style={styles.searchInputContainer}>
                        <TextInput
                          style={styles.searchInput}
                          placeholder="Search messages..."
                          value={searchTerm}
                          onChangeText={setSearchTerm}
                          autoFocus
                        />
                        <IconButton
                          icon="close"
                          onPress={() => {
                            setSearchTerm('');
                            setIsSearchExpanded(false);
                          }}
                          size={20}
                        />
                      </View>
                    ) : (
                      <IconButton
                        icon="magnify"
                        onPress={() => setIsSearchExpanded(true)}
                        size={24}
                      />
                    )}
                  </View>
                </View>

                <FlatList
                  ref={flatListRef}
                  data={displayedMessages}
                  keyExtractor={(item) => item.id}
                  renderItem={({ item }) => (
                    <ChatMessage
                      message={item.text}
                      isCurrentUser={item.senderId === user?.id}
                      timestamp={item.timestamp}
                      senderName={item.senderName}
                      attachment={item.attachment}
                    />
                  )}
                  contentContainerStyle={styles.messagesContainer}
                  onScroll={handleScroll}
                />
                <ChatInput onSend={handleSend} />
              </>
            ) : (
              <View style={styles.noCounsellorSelected}>
                <Text>Select a counsellor to start chatting</Text>
              </View>
            )}
          </View>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row'
  },
  leftPanel: {
    width: '30%',
    borderRightWidth: 1,
    borderRightColor: '#e1e1e1',
    backgroundColor: '#fff'
  },
  rightPanel: {
    flex: 1,
    backgroundColor: '#fff'
  },
  counsellorSearchContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1'
  },
  counsellorSearchInput: {
    padding: 8,
    borderWidth: 1,
    borderColor: '#e1e1e1',
    borderRadius: 8
  },
  counsellorList: {
    padding: 8
  },
  counsellorItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0'
  },
  selectedCounsellor: {
    backgroundColor: '#f0f0f0'
  },
  counsellorName: {
    fontSize: 16,
    fontWeight: 'bold'
  },
  badge: {
    backgroundColor: '#00A77E', // Using primary color directly
    borderRadius: 12,
    paddingHorizontal: 6,
    paddingVertical: 2
  },
  badgeText: {
    color: '#fff',
    fontSize: 12
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1'
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 4
  },
  searchInput: {
    flex: 1,
    padding: 8,
    marginRight: 4,
    minWidth: 150
  },
  messagesContainer: {
    padding: 16
  },
  fullWidthChatContainer: {
    flex: 1
  },
  fullWidthCounsellorList: {
    flex: 1,
    padding: 16
  },
  noCounsellorSelected: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  }
});