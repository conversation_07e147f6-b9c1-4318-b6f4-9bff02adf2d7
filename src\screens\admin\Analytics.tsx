import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Dimensions } from 'react-native';
import { Text, Button, Card, Surface, ActivityIndicator } from 'react-native-paper';
import { theme, styles as globalStyles } from '../../theme';
import { useAuth, API_BASE_URL } from '../../context/AuthContext';
import { useIsFocused } from '@react-navigation/native';
import axios from 'axios';
import {
  <PERSON><PERSON>hart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
} from 'recharts';

// Custom tick formatter for age groups to handle many intervals
const formatAgeTick = (tickItem: string) => {
  // For the last age group (e.g., "72+"), return as is
  if (tickItem.includes('+')) return tickItem;
  return tickItem;
};

// Simple responsive breakpoint - consistent with other dashboard components
const SCREEN_BREAKPOINT_TABLET = 768;

// Colors for the charts
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

// Enhanced color palette for bubble chart with better accessibility and contrast
const BUBBLE_COLORS = [
  '#1f77b4', // Blue
  '#ff7f0e', // Orange
  '#2ca02c', // Green
  '#d62728', // Red
  '#9467bd', // Purple
  '#8c564b', // Brown
  '#e377c2', // Pink
  '#7f7f7f', // Gray
  '#bcbd22', // Olive
  '#17becf', // Cyan
  '#aec7e8', // Light Blue
  '#ffbb78', // Light Orange
  '#98df8a', // Light Green
  '#ff9896', // Light Red
  '#c5b0d5', // Light Purple
];

// Color palette for Age Distribution bar chart
const AGE_COLORS = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8',
  '#F7DC6F', '#BB8FCE', '#85C1E9', '#F8C471', '#82E0AA', '#F1948A', '#85C1E9',
  '#F4D03F', '#AED6F1', '#A9DFBF', '#F5B7B1', '#D7BDE2', '#A3E4D7', '#FCF3CF',
  '#FADBD8', '#E8DAEF', '#D1F2EB', '#FEF9E7'
];

// Color palette for Ethnicity Distribution bar chart
const ETHNICITY_COLORS = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'
];

// StatCard component for displaying statistics
const StatCard = ({ number, label }: { number: number; label: string }) => (
  <Surface style={styles.statCard}>
    <Text style={styles.statNumber}>{number}</Text>
    <Text style={styles.statLabel}>{label}</Text>
  </Surface>
);

// Function to get contrasting text color for readability (for future use)
const getContrastColor = (backgroundColor: string) => {
  // Simple contrast calculation - use white text for dark backgrounds, black for light
  const hex = backgroundColor.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;
  return brightness > 128 ? '#000000' : '#FFFFFF';
};

// Legend component for the charts
const BubbleChartLegend = ({ legendData }: { legendData: any[] }) => {
  if (!legendData || legendData.length === 0) return null;

  return (
    <View style={styles.legendContainer}>
      <View style={styles.legendItems}>
        {legendData.map((item, index) => (
          <View key={index} style={styles.legendItem}>
            <View style={[styles.legendColorBox, { backgroundColor: item.color }]} />
            <Text style={styles.legendText} numberOfLines={1}>
              {item.name}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );
};

export const Analytics = () => {
  const { user, token } = useAuth() as { user: any; token: string | null };
  const isFocused = useIsFocused();
  // We still need to track screen width for responsive layout
  const [, setScreenWidth] = useState(Dimensions.get('window').width);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Data for charts
  const [genderData, setGenderData] = useState<any[]>([]);
  const [ageGroupData, setAgeGroupData] = useState<any[]>([]);
  const [ethnicityData, setEthnicityData] = useState<any[]>([]);
  const [allergyCardData, setAllergyCardData] = useState<any[]>([]);
  const [reactionFrequencyData, setReactionFrequencyData] = useState<any[]>([]);
  const [legendData, setLegendData] = useState<any[]>([]);
  const [ageGroupLegendData, setAgeGroupLegendData] = useState<any[]>([]);
  const [ethnicityLegendData, setEthnicityLegendData] = useState<any[]>([]);

  // Statistics data
  const [stats, setStats] = useState({
    totalPatients: 0,
    totalReactions: 0,
    pendingReactions: 0,
    resolvedReactions: 0
  });

  // Update screen width when dimensions change
  useEffect(() => {
    const updateLayout = () => {
      setScreenWidth(Dimensions.get('window').width);
    };

    // Add event listener for dimension changes
    const subscription = Dimensions.addEventListener('change', updateLayout);

    // Clean up event listener on component unmount
    return () => subscription.remove();
  }, []);

  // Fetch data for charts
  useEffect(() => {
    let isActive = true;

    const fetchData = async () => {
      if (!user) {
        if (isActive) {
          setError('User not found');
          setLoading(false);
        }
        return;
      }

      if (!token) {
        if (isActive) {
          setError('Authentication token not found. Please log in again.');
          setLoading(false);
        }
        return;
      }

      try {
        // Fetch demographics data, reaction statistics, allergy card statistics, and reaction frequency in parallel
        const [demographicsResponse, reactionsStatsResponse, allergyCardStatsResponse, reactionFrequencyResponse] = await Promise.all([
          axios.get(`${API_BASE_URL}/api/patients/demographics`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          }),
          axios.get(`${API_BASE_URL}/api/drug-reactions/stats/total`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          }),
          axios.get(`${API_BASE_URL}/api/drug-reactions/stats/allergy-cards`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          }),
          axios.get(`${API_BASE_URL}/api/drug-reactions/stats/reaction-frequency`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          })
        ]);

        const { genderDistribution, ageGroupDistribution, ethnicityDistribution, totalPatients } = demographicsResponse.data;
        const reactionsStats = reactionsStatsResponse.data;
        const allergyCardStats = allergyCardStatsResponse.data;
        const reactionFrequency = reactionFrequencyResponse.data;

        if (isActive) {
          // Process gender distribution for chart - only include Male and Female
          const filteredGenderData = Object.entries(genderDistribution)
            .filter(([key]) => key === 'Male' || key === 'Female')
            .map(([key, value]) => ({
              name: key,
              value: value as number
            }));

          // Process age group distribution for chart
          // Sort age groups in ascending order
          const ageGroupChartData = Object.keys(ageGroupDistribution)
            .map((key, index) => ({
              name: key,
              count: ageGroupDistribution[key as keyof typeof ageGroupDistribution],
              // Extract the first number from the range for sorting
              sortValue: parseInt(key.split('-')[0]),
              fill: AGE_COLORS[index % AGE_COLORS.length] // Add color for each age group
            }))
            .sort((a, b) => a.sortValue - b.sortValue)
            .map(({ name, count, fill }) => ({ name, count, fill })); // Keep the fill property

          // Process ethnicity distribution for chart
          const ethnicityChartData = Object.entries(ethnicityDistribution)
            .map(([key, value], index) => ({
              name: key || 'Not Specified',
              count: value as number,
              fill: ETHNICITY_COLORS[index % ETHNICITY_COLORS.length] // Add color for each ethnicity
            }))
            .sort((a, b) => b.count - a.count) // Sort by count in descending order
            .slice(0, 8); // Take top 8 ethnicities for better visualization

          // Process allergy card distribution for pie chart
          const allergyCardChartData = [
            { name: 'With Allergy Card', value: allergyCardStats.withAllergyCard },
            { name: 'Without Allergy Card', value: allergyCardStats.withoutAllergyCard }
          ];

          // Process reaction frequency data - create simple scatter plot data
          const topReactions = reactionFrequency.slice(0, 10); // Limit to top 10 drugs

          // Create simple scatter chart data with drug name on x-axis and count on y-axis
          const reactionFrequencyChartData = topReactions.map((item: any, index: number) => ({
            name: item.name,
            x: index + 1, // Position on x-axis (1, 2, 3, etc.)
            y: item.count, // Count on y-axis
            count: item.count,
            fill: BUBBLE_COLORS[index % BUBBLE_COLORS.length] // Assign color for each drug
          }));

          // Create legend data for the scatter chart
          const legendData = topReactions.map((item: any, index: number) => ({
            name: item.name,
            color: BUBBLE_COLORS[index % BUBBLE_COLORS.length]
          }));

          // Create legend data for age groups
          const ageGroupLegendData = ageGroupChartData.map((ageGroup) => ({
            name: ageGroup.name,
            color: ageGroup.fill
          }));

          // Create legend data for ethnicity groups
          const ethnicityLegendData = ethnicityChartData.map((ethnicity) => ({
            name: ethnicity.name,
            color: ethnicity.fill
          }));

          // Set chart data
          setGenderData(filteredGenderData);
          setAgeGroupData(ageGroupChartData);
          setEthnicityData(ethnicityChartData);
          setAllergyCardData(allergyCardChartData);
          setReactionFrequencyData(reactionFrequencyChartData);
          setLegendData(legendData);
          setAgeGroupLegendData(ageGroupLegendData);
          setEthnicityLegendData(ethnicityLegendData);

          // Set statistics data
          setStats({
            totalPatients,
            totalReactions: reactionsStats.total,
            pendingReactions: reactionsStats.pending,
            resolvedReactions: reactionsStats.resolved
          });
        }
      } catch (err) {
        if (isActive) {
          setError('Error fetching data');
          console.error(err);
        }
      } finally {
        if (isActive) {
          setLoading(false);
        }
      }
    };

    if (isFocused) {
      fetchData();
    }

    return () => {
      isActive = false;
    };
  }, [isFocused, user, token]);

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
        </View>
      );
    }

    if (error) {
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <Button
            mode="contained"
            onPress={() => setLoading(true)}
            style={styles.button}
          >
            Retry
          </Button>
        </View>
      );
    }

    return (
      <>
        {/* Statistics Summary Cards */}
        <View style={styles.statsContainer}>
          <StatCard number={stats.totalPatients} label="Total Patients" />
          <StatCard number={stats.totalReactions} label="Total Reactions" />
          <StatCard number={stats.pendingReactions} label="Pending" />
          <StatCard number={stats.resolvedReactions} label="Resolved" />
        </View>

        {/* First row of charts */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.cardTitle}>Patient Demographics</Text>
            <View style={styles.chartsRow}>
              {/* Age Distribution Bar Chart (Left) */}
              <View style={styles.chartContainer}>
                <Text style={styles.chartTitle}>Age Distribution (3-Year Intervals)</Text>
                <ResponsiveContainer width="100%" height={250}>
                  <BarChart
                    data={ageGroupData}
                    margin={{
                      top: 5,
                      right: 10,
                      left: 0,
                      bottom: 30, // Increased bottom margin for rotated labels
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="name"
                      angle={-45}
                      textAnchor="end"
                      height={70}
                      interval={0}
                      tick={{ fontSize: 10 }}
                      tickFormatter={formatAgeTick}
                    />
                    <YAxis />
                    <Tooltip formatter={(value) => [`${value} patients`, 'Count']} />
                    <Bar dataKey="count" name="Patients">
                      {ageGroupData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.fill} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>

                {/* Legend for the age distribution chart */}
                <BubbleChartLegend legendData={ageGroupLegendData} />
              </View>

              {/* Gender Distribution Pie Chart (Right) */}
              <View style={styles.chartContainer}>
                <Text style={styles.chartTitle}>Gender Distribution</Text>
                <ResponsiveContainer width="100%" height={250}>
                  <PieChart>
                    <Pie
                      data={genderData}
                      cx="50%"
                      cy="50%"
                      labelLine={true}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {genderData.map((_, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Second row of charts */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.cardTitle}>Additional Patient Insights</Text>
            <View style={styles.chartsRow}>
              {/* Ethnicity Distribution Bar Chart (Left) */}
              <View style={styles.chartContainer}>
                <Text style={styles.chartTitle}>Ethnicity Distribution</Text>
                <ResponsiveContainer width="100%" height={250}>
                  <BarChart
                    data={ethnicityData}
                    margin={{
                      top: 5,
                      right: 10,
                      left: 0,
                      bottom: 50, // Increased bottom margin for rotated labels
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="name"
                      angle={-45}
                      textAnchor="end"
                      height={70}
                      interval={0}
                      tick={{ fontSize: 11 }}
                    />
                    <YAxis />
                    <Tooltip formatter={(value) => [`${value} patients`, 'Count']} />
                    <Bar dataKey="count" name="Patients">
                      {ethnicityData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.fill} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>

                {/* Legend for the ethnicity distribution chart */}
                <BubbleChartLegend legendData={ethnicityLegendData} />
              </View>

              {/* Allergy Card Distribution Pie Chart (Right) */}
              <View style={styles.chartContainer}>
                <Text style={styles.chartTitle}>Allergy Card Status</Text>
                <ResponsiveContainer width="100%" height={250}>
                  <PieChart>
                    <Pie
                      data={allergyCardData}
                      cx="50%"
                      cy="50%"
                      labelLine={true}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      <Cell fill="#0088FE" />
                      <Cell fill="#FF8042" />
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Third row of charts - Drug Reaction Frequency */}
        <Card style={[styles.card, styles.lastCard]}>
          <Card.Content>
            <Text style={styles.cardTitle}>Drug Reaction Analysis</Text>
            <View style={styles.fullWidthChartContainer}>
              <Text style={styles.chartTitle}>Most Frequently Reported Drug Reactions</Text>
              <ResponsiveContainer width="100%" height={350}>
                <ScatterChart
                  data={reactionFrequencyData}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 60,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    type="number"
                    dataKey="x"
                    domain={[0, 11]}
                    tick={{ fontSize: 12 }}
                    label={{ value: 'Drug Index', position: 'insideBottom', offset: -10 }}
                  />
                  <YAxis
                    type="number"
                    dataKey="y"
                    tick={{ fontSize: 12 }}
                    label={{ value: 'Number of Reports', angle: -90, position: 'insideLeft' }}
                  />
                  <Tooltip
                    content={({ active, payload }) => {
                      if (active && payload && payload.length > 0) {
                        const data = payload[0].payload;
                        return (
                          <div style={{
                            backgroundColor: theme.colors.surface,
                            padding: '8px 12px',
                            border: `1px solid ${theme.colors.outline}`,
                            borderRadius: '4px',
                            boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                          }}>
                            <p style={{
                              margin: 0,
                              color: theme.colors.text,
                              fontWeight: 'bold',
                              fontSize: '14px'
                            }}>
                              {data.name}
                            </p>
                            <p style={{
                              margin: '4px 0 0 0',
                              color: theme.colors.text,
                              fontSize: '12px'
                            }}>
                              Reports: {data.count}
                            </p>
                          </div>
                        );
                      }
                      return null;
                    }}
                  />
                  <Scatter dataKey="y" fill={theme.colors.primary}>
                    {reactionFrequencyData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.fill} />
                    ))}
                  </Scatter>
                </ScatterChart>
              </ResponsiveContainer>

              {/* Legend for the scatter chart */}
              <BubbleChartLegend legendData={legendData} />
            </View>
          </Card.Content>
        </Card>
      </>
    );
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <View>
          <Text style={styles.title}>Analytics Dashboard</Text>
          <Text style={styles.subtitle}>Patient Demographics</Text>
        </View>
      </View>

      {renderContent()}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    padding: theme.spacing.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.text,
    opacity: 0.7,
    marginTop: theme.spacing.xs,
  },
  backIcon: {
    margin: 0,
  },
  // Stats cards styles
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
    marginBottom: theme.spacing.lg,
  },
  statCard: {
    flex: 1,
    minWidth: 80,
    margin: theme.spacing.xs,
    padding: theme.spacing.sm,
    alignItems: 'center',
    borderRadius: theme.roundness,
    elevation: 2,
    backgroundColor: theme.colors.surface,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  statLabel: {
    fontSize: 12,
    color: theme.colors.text,
    textAlign: 'center',
    marginTop: theme.spacing.xs,
  },
  // Loading and error styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
  },
  errorText: {
    color: theme.colors.error,
    marginBottom: theme.spacing.md,
    textAlign: 'center',
  },
  // Card styles
  card: {
    ...globalStyles.card,
    marginVertical: theme.spacing.sm,
  },
  lastCard: {
    marginBottom: theme.spacing.xl,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.md,
  },
  // Charts styles
  chartsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  chartContainer: {
    width: Dimensions.get('window').width > SCREEN_BREAKPOINT_TABLET ? '48%' : '100%',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginBottom: Dimensions.get('window').width <= SCREEN_BREAKPOINT_TABLET ? theme.spacing.md : 0,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  chartSubtitle: {
    fontSize: 12,
    color: theme.colors.text,
    opacity: 0.7,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  fullWidthChartContainer: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: theme.spacing.md,
  },
  button: {
    marginVertical: theme.spacing.xs,
  },
  // Legend styles
  legendContainer: {
    marginTop: theme.spacing.xs,
    padding: theme.spacing.xs,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.roundness,
    elevation: 1,
  },
  legendTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  legendItems: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'center',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: theme.spacing.xs / 2,
    marginVertical: theme.spacing.xs / 4,
    paddingHorizontal: theme.spacing.xs / 2,
    paddingVertical: theme.spacing.xs / 4,
    backgroundColor: theme.colors.background,
    borderRadius: theme.roundness / 2,
    elevation: 1,
  },
  legendColorBox: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: theme.spacing.xs / 2,
    borderWidth: 1,
    borderColor: theme.colors.outline || '#E0E0E0',
  },
  legendText: {
    fontSize: 10,
    color: theme.colors.text,
    maxWidth: 70,
  },
});

export default Analytics;
