require('dotenv').config();
const express = require('express');
const mongoose = require('mongoose');
const bodyParser = require('body-parser');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const cors = require('cors');
const http = require('http');
const { Server } = require('socket.io');
const User = require('./models/User');
const Message = require('./models/Message');
const auth = require('./middleware/auth');

// Import Routes
const staffRoutes = require('./routes/staff');
const patientRoutes = require('./routes/patients');
const drugReactionRoutes = require('./routes/drugReactions');
const chatRoutes = require('./routes/chat');
const drugCounsellingRoutes = require('./routes/drugCounselling');

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: '*',
    methods: ['GET', 'POST']
  }
});

// Make io accessible to routes
app.set('io', io);

app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));

const PORT = process.env.PORT || 5000;
const JWT_SECRET = process.env.JWT_SECRET;

// Connect to MongoDB with improved settings
mongoose.connect(process.env.MONGO_URI, {
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  family: 4,
})
.then(() => console.log('MongoDB connected'))
.catch(err => console.error('MongoDB connection error:', err));

/** ========== SOCKET.IO CONNECTION ========== */
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  socket.on('join-room', (userId) => {
    if (!userId) return;

    socket.join(userId);
    console.log(`User ${userId} joined room`);
  });

  socket.on('send-message', async (messageData) => {
    try {
      const { sender, receiver, content, attachment } = messageData;
      if (!sender || !receiver || (!content && !attachment)) {
        socket.emit('error', { error: 'Missing sender, receiver, or message content/attachment' });
        return;
      }
      console.log("Socket message received with:", { sender, receiver, content, hasAttachment: !!attachment });
      // Validate sender and receiver IDs
      if (!mongoose.Types.ObjectId.isValid(sender) || !mongoose.Types.ObjectId.isValid(receiver)) {
        throw new Error('Invalid sender or receiver ID');
      }

      // Validate attachment if present
      if (attachment) {
        // Check file size (limit to 10MB)
        const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB in bytes
        if (attachment.fileData && Buffer.from(attachment.fileData, 'base64').length > MAX_FILE_SIZE) {
          socket.emit('error', { error: 'File size exceeds the 10MB limit' });
          return;
        }

        // Validate file type
        const allowedFileTypes = ['image', 'document', 'audio', 'video', 'other'];
        if (!allowedFileTypes.includes(attachment.fileType)) {
          socket.emit('error', { error: 'Invalid file type' });
          return;
        }
      }

      // Save message to database
      const message = new Message({
        senderId: sender,
        receiverId: receiver,
        content: content || '',
        attachment,
        timestamp: new Date()
      });

      const savedMessage = await message.save();
      const populatedMessage = await Message.findById(savedMessage._id)
        .populate('senderId', 'name')
        .populate('receiverId', 'name');

      // Emit to both rooms
      const messagePayload = {
        id: savedMessage._id,
        text: content || '',
        attachment: attachment || null,
        timestamp: savedMessage.timestamp,
        senderId: sender,
        senderName: populatedMessage.senderId.name,
        receiverId: receiver
      };

      io.to(receiver).emit('receive-message', messagePayload);
      io.to(sender).emit('receive-message', messagePayload);
    } catch (error) {
      console.error('Error handling socket message:', error);
    }
  });

  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
});

// Route Registrations
app.use('/api/staff', staffRoutes);
app.use('/api/patients', patientRoutes);
app.use('/api/drug-reactions', drugReactionRoutes);
app.use('/api/chat', chatRoutes);
app.use('/api/drugCounselling', drugCounsellingRoutes);

// User Registration
app.post('/api/register', async (req, res) => {
  const {
    email, password, role, hospitalNo, firstName, lastName,
    age, sex, weight, contactNumber, ethnicity,
    pregnant, dateOfBirth, priorCounsel, username
  } = req.body;

  try {
    const existingUser = await User.findOne({
      $or: [
        { email: email },
        { username: username || email }
      ]
    });

    if (existingUser) {
      const errorMessage = existingUser.email === email
        ? 'Email already registered'
        : 'Username already taken';
      console.log('Registration failed:', errorMessage); // Add logging
      return res.status(400).json({ error: errorMessage });
    }

    const validPregnantValues = ['Yes', 'No', 'Not Applicable'];
    let validatedPregnant = pregnant && pregnant.trim() !== '' ? pregnant : 'Not Applicable';

    if (!validPregnantValues.includes(validatedPregnant)) {
      return res.status(400).json({ error: 'Pregnant must be Yes or No' });
    }

    const hashedPassword = await bcrypt.hash(password, 10);
    const newUser = new User({
      email,
      password: hashedPassword,
      name: `${firstName} ${lastName}`,
      role,
      firstName,
      lastName,
      hospitalNo,
      age,
      sex,
      weight,
      contactNumber,
      ethnicity,
      pregnant: validatedPregnant,
      dateOfBirth,
      priorCounsel,
      username: username || email
    });

    const savedUser = await newUser.save();
    res.status(201).json({ message: 'User registered successfully', user: savedUser });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(400).json({ error: 'Error registering user', details: error.message });
  }
});

// User Login
app.post('/api/login', async (req, res) => {
  const { email, password } = req.body;

  try {
    const user = await User.findOne({
      $or: [
        { email: email },
        { username: email }
      ]
    });

    if (!user) {
      return res.status(400).json({ error: 'Invalid email/username or password' });
    }

    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(400).json({ error: 'Invalid email/username or password' });
    }

    const token = jwt.sign({ _id: user._id.toString() }, JWT_SECRET);
    res.status(200).json({ message: 'Login successful', user, token });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Protected Route Example
app.get('/api/protected', auth, (req, res) => {
  res.send(`Hello ${req.user.name || req.user.email}, you have access to this protected route!`);
});

server.listen(PORT, () => {
  console.log(`Server is running on http://localhost:${PORT}`);
});