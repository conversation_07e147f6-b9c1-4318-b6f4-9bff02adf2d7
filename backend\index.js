require('dotenv').config();
const express = require('express');
const mongoose = require('mongoose');
const bodyParser = require('body-parser');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const cors = require('cors');
const http = require('http');
const { Server } = require('socket.io');
const User = require('./models/User');
const Message = require('./models/Message');
const auth = require('./middleware/auth');
const sendEmail = require('./utils/mailer');
const crypto = require('crypto');

// Import Routes
const staffRoutes = require('./routes/staff');
const patientRoutes = require('./routes/patients');
const drugReactionRoutes = require('./routes/drugReactions');
const chatRoutes = require('./routes/chat');
const drugCounsellingRoutes = require('./routes/drugCounselling');

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: '*',
    methods: ['GET', 'POST']
  }
});

// Make io accessible to routes
app.set('io', io);

app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));

const PORT = process.env.PORT || 5000;
const JWT_SECRET = process.env.JWT_SECRET;

// In-memory storage for password reset tokens (in production, use Redis or database)
const passwordResetTokens = new Map();
const resetRequestCounts = new Map();

// Connect to MongoDB with improved settings
mongoose.connect(process.env.MONGO_URI, {
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  family: 4,
})
.then(() => console.log('MongoDB connected'))
.catch(err => console.error('MongoDB connection error:', err));

/** ========== SOCKET.IO CONNECTION ========== */
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  socket.on('join-room', (userId) => {
    if (!userId) return;

    socket.join(userId);
    console.log(`User ${userId} joined room`);
  });

  socket.on('send-message', async (messageData) => {
    try {
      const { sender, receiver, content, attachment } = messageData;
      if (!sender || !receiver || (!content && !attachment)) {
        socket.emit('error', { error: 'Missing sender, receiver, or message content/attachment' });
        return;
      }
      console.log("Socket message received with:", { sender, receiver, content, hasAttachment: !!attachment });
      // Validate sender and receiver IDs
      if (!mongoose.Types.ObjectId.isValid(sender) || !mongoose.Types.ObjectId.isValid(receiver)) {
        throw new Error('Invalid sender or receiver ID');
      }

      // Validate attachment if present
      if (attachment) {
        // Check file size (limit to 10MB)
        const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB in bytes
        if (attachment.fileData && Buffer.from(attachment.fileData, 'base64').length > MAX_FILE_SIZE) {
          socket.emit('error', { error: 'File size exceeds the 10MB limit' });
          return;
        }

        // Validate file type
        const allowedFileTypes = ['image', 'document', 'audio', 'video', 'other'];
        if (!allowedFileTypes.includes(attachment.fileType)) {
          socket.emit('error', { error: 'Invalid file type' });
          return;
        }
      }

      // Save message to database
      const message = new Message({
        senderId: sender,
        receiverId: receiver,
        content: content || '',
        attachment,
        timestamp: new Date()
      });

      const savedMessage = await message.save();
      const populatedMessage = await Message.findById(savedMessage._id)
        .populate('senderId', 'name')
        .populate('receiverId', 'name');

      // Emit to both rooms
      const messagePayload = {
        id: savedMessage._id,
        text: content || '',
        attachment: attachment || null,
        timestamp: savedMessage.timestamp,
        senderId: sender,
        senderName: populatedMessage.senderId.name,
        receiverId: receiver
      };

      io.to(receiver).emit('receive-message', messagePayload);
      io.to(sender).emit('receive-message', messagePayload);
    } catch (error) {
      console.error('Error handling socket message:', error);
    }
  });

  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
});

// Route Registrations
app.use('/api/staff', staffRoutes);
app.use('/api/patients', patientRoutes);
app.use('/api/drug-reactions', drugReactionRoutes);
app.use('/api/chat', chatRoutes);
app.use('/api/drugCounselling', drugCounsellingRoutes);

// User Registration
app.post('/api/register', async (req, res) => {
  const {
    email, password, role, hospitalNo, firstName, lastName,
    age, sex, weight, contactNumber, ethnicity,
    pregnant, dateOfBirth, priorCounsel, username
  } = req.body;

  try {
    const existingUser = await User.findOne({
      $or: [
        { email: email },
        { username: username || email }
      ]
    });

    if (existingUser) {
      const errorMessage = existingUser.email === email
        ? 'Email already registered'
        : 'Username already taken';
      console.log('Registration failed:', errorMessage); // Add logging
      return res.status(400).json({ error: errorMessage });
    }

    const validPregnantValues = ['Yes', 'No', 'Not Applicable'];
    let validatedPregnant = pregnant && pregnant.trim() !== '' ? pregnant : 'Not Applicable';

    if (!validPregnantValues.includes(validatedPregnant)) {
      return res.status(400).json({ error: 'Pregnant must be Yes or No' });
    }

    const hashedPassword = await bcrypt.hash(password, 10);
    const newUser = new User({
      email,
      password: hashedPassword,
      name: `${firstName} ${lastName}`,
      role,
      firstName,
      lastName,
      hospitalNo,
      age,
      sex,
      weight,
      contactNumber,
      ethnicity,
      pregnant: validatedPregnant,
      dateOfBirth,
      priorCounsel,
      username: username || email
    });

    const savedUser = await newUser.save();
    res.status(201).json({ message: 'User registered successfully', user: savedUser });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(400).json({ error: 'Error registering user', details: error.message });
  }
});

// User Login
app.post('/api/login', async (req, res) => {
  const { email, password } = req.body;

  try {
    const user = await User.findOne({
      $or: [
        { email: email },
        { username: email }
      ]
    });

    if (!user) {
      return res.status(400).json({ error: 'Invalid email/username or password' });
    }

    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(400).json({ error: 'Invalid email/username or password' });
    }

    const token = jwt.sign({ _id: user._id.toString() }, JWT_SECRET);
    res.status(200).json({ message: 'Login successful', user, token });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Forgot Password
app.post('/api/forgot-password', async (req, res) => {
  const { email } = req.body;

  try {
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email || !emailRegex.test(email)) {
      return res.status(400).json({ error: 'Please provide a valid email address' });
    }

    // Rate limiting: max 3 requests per email per hour
    const now = Date.now();
    const hourAgo = now - (60 * 60 * 1000);
    const requestKey = `reset_${email}`;

    if (!resetRequestCounts.has(requestKey)) {
      resetRequestCounts.set(requestKey, []);
    }

    const requests = resetRequestCounts.get(requestKey).filter(time => time > hourAgo);

    if (requests.length >= 3) {
      return res.status(429).json({ error: 'Too many reset requests. Please try again later.' });
    }

    requests.push(now);
    resetRequestCounts.set(requestKey, requests);

    // Check if user exists (but don't reveal this information)
    const user = await User.findOne({ email: email });

    // Always respond with success for security (don't reveal if email exists)
    if (user) {
      // Generate secure reset token
      const resetToken = crypto.randomBytes(32).toString('hex');
      const tokenExpiry = Date.now() + (30 * 60 * 1000); // 30 minutes

      // Store token
      passwordResetTokens.set(resetToken, {
        userId: user._id.toString(),
        email: user.email,
        expiry: tokenExpiry
      });

      // Send reset email
      const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/reset-password?token=${resetToken}`;

      const emailSubject = 'Password Reset Request - ADR System';
      const emailText = `Dear ${user.name || 'User'},

You have requested to reset your password for the Adverse Drug Reaction Reporting System.

Please click the link below to reset your password:
${resetUrl}

This link will expire in 30 minutes.

If you did not request this password reset, please ignore this email.

Best regards,
ADR System Team`;

      const emailHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2196F3;">Password Reset Request</h2>
          <p>Dear ${user.name || 'User'},</p>
          <p>You have requested to reset your password for the Adverse Drug Reaction Reporting System.</p>
          <p>Please click the button below to reset your password:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" style="background-color: #2196F3; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">Reset Password</a>
          </div>
          <p><strong>This link will expire in 30 minutes.</strong></p>
          <p>If you did not request this password reset, please ignore this email.</p>
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 12px;">Best regards,<br>ADR System Team</p>
        </div>
      `;

      await sendEmail({
        to: user.email,
        subject: emailSubject,
        text: emailText,
        html: emailHtml
      });

      console.log(`Password reset email sent to ${user.email}`);
    }

    // Always return success response (security best practice)
    res.status(200).json({
      message: 'If an account with that email exists, we have sent password reset instructions.'
    });

  } catch (error) {
    console.error('Forgot password error:', error);
    res.status(500).json({ error: 'Server error. Please try again later.' });
  }
});

// Protected Route Example
app.get('/api/protected', auth, (req, res) => {
  res.send(`Hello ${req.user.name || req.user.email}, you have access to this protected route!`);
});

server.listen(PORT, () => {
  console.log(`Server is running on http://localhost:${PORT}`);
});