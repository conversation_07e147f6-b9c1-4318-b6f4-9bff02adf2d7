import React, { useEffect, useState, useCallback } from 'react';
import { View, StyleSheet, ScrollView, ActivityIndicator, Dimensions } from 'react-native';
import { Text, Button, Card, IconButton, Surface, Badge, Dialog, Portal, Paragraph, Avatar, useTheme } from 'react-native-paper';
import { theme, styles as globalStyles } from '../../theme';
import { useAuth, API_BASE_URL } from '../../context/AuthContext';
import { DrugReaction } from '../../types/drugReaction';
import axios from 'axios';
import { useFocusEffect } from '@react-navigation/native';

// Simple responsive breakpoint - consistent with other dashboard components
const SCREEN_BREAKPOINT_TABLET = 768;

const StatCard = ({ number, label }: { number: number; label: string }) => (
    <Surface style={styles.statCard}>
        <Text style={styles.statNumber}>{number}</Text>
        <Text style={styles.statLabel}>{label}</Text>
    </Surface>
);

export const CounsellorDashboard = ({ navigation }: any) => {
    const { user, logout } = useAuth();
    const paperTheme = useTheme(); // Add useTheme hook for accessing theme in the component
    const [screenWidth, setScreenWidth] = useState(Dimensions.get('window').width);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [stats, setStats] = useState({
        total: 0,
        pending: 0,
        resolved: 0,
        bySeverity: {
            mild: 0,
            moderate: 0,
            severe: 0
        }
    });
    const [recentReactions, setRecentReactions] = useState<DrugReaction[]>([]);
    const [logoutDialogVisible, setLogoutDialogVisible] = useState(false);

    // Define fetchData function at component level so it can be called from retry button
    const fetchData = async () => {
        if (!user) {
            setError('User not found');
            setLoading(false);
            return;
        }

        try {
            const statsResponse = await axios.get(`${API_BASE_URL}/api/drug-reactions/stats/total`);
            const reactionsResponse = await axios.get(`${API_BASE_URL}/api/drug-reactions`);

            const reactions: DrugReaction[] = reactionsResponse.data;

            const pendingReactions = reactions
                .filter(r => r.status === 'pending')
                .sort((a, b) => new Date(b.dateReported).getTime() - new Date(a.dateReported).getTime())
                .slice(0, 3);

            setStats(statsResponse.data);
            setRecentReactions(pendingReactions);
            setError('');
        } catch (err) {
            setError('Error fetching statistics');
            console.error(err);
        } finally {
            setLoading(false);
        }
    };

    // Update screen width when dimensions change
    useEffect(() => {
        const updateLayout = () => {
            setScreenWidth(Dimensions.get('window').width);
        };

        // Add event listener for dimension changes
        const subscription = Dimensions.addEventListener('change', updateLayout);

        // Clean up event listener on component unmount
        return () => subscription.remove();
    }, []);

    // Function to show the logout confirmation dialog
    const showLogoutDialog = () => {
        setLogoutDialogVisible(true);
    };

    // Function to handle logout confirmation
    const handleLogoutConfirm = async () => {
        setLogoutDialogVisible(false);
        await logout();
    };

    // Function to dismiss the logout dialog
    const hideLogoutDialog = () => {
        setLogoutDialogVisible(false);
    };

    // Use focus effect to fetch data when screen comes into focus
    useFocusEffect(
        useCallback(() => {
          let isActive = true;

          const fetchDataWithCleanup = async () => {
            if (!isActive) return;

            setLoading(true);
            await fetchData();
          };

          fetchDataWithCleanup();

          return () => {
            isActive = false; // cleanup if screen is unfocused
          };
        }, [user])
      );
    const renderStats = () => {
        if (loading) {
            return (
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={theme.colors.primary} />
                </View>
            );
        }

        // Display error message if there was an error fetching data
        if (error) {
            return (
                <View style={styles.errorContainer}>
                    <Text style={styles.errorText}>{error}</Text>
                    <Button
                        mode="contained"
                        onPress={() => {
                            setLoading(true);
                            setError('');
                            fetchData();
                        }}
                        style={{ marginTop: 20 }}
                    >
                        Retry
                    </Button>
                </View>
            );
        }

        return (
            <View style={styles.statsContainer}>
                <StatCard number={stats.total} label="Total Cases" />
                <StatCard number={stats.pending} label="Pending Cases" />
                <StatCard number={stats.resolved} label="Resolved" />
            </View>
        );
    };

    const getSeverityColor = (severity: string) => {
        switch (severity) {
            case 'mild':
                return theme.colors.primary;
            case 'moderate':
                return '#FFA500';
            case 'severe':
                return theme.colors.error;
            default:
                return theme.colors.text;
        }
    };

    const countPendingSevereReactions = (reactions: DrugReaction[]) => {
        return reactions.filter(r => r.severity === 'severe' && r.status === 'pending').length;
    };

    return (
        <ScrollView style={styles.container}>
            <View style={styles.header}>
                <View>
                    <Text style={styles.title}>Counsellor Dashboard</Text>
                    <Text style={styles.subtitle}>Welcome, {user?.name}</Text>
                </View>
                <IconButton
                    icon="logout"
                    size={24}
                    iconColor={theme.colors.primary}
                    onPress={showLogoutDialog}
                />
            </View>

            {renderStats()}

            <Card style={styles.card}>
                <Card.Content>
                    <View style={styles.cardHeader}>
                        <Text style={styles.cardTitle}>Reaction Management</Text>
                        {stats.pending > 0 && (
                            <Badge size={24} style={styles.badge}>{stats.pending}</Badge>
                        )}
                    </View>
                    <Button
                        mode="contained"
                        icon="clipboard-alert"
                        onPress={() => navigation.navigate('ReactionManagement')}
                        style={styles.button}
                        buttonColor={theme.colors.primary}
                    >
                        Manage Reactions
                    </Button>
                    {countPendingSevereReactions(recentReactions) > 0 && (
                        <View style={styles.alertContainer}>
                            <Text style={styles.alertText}>
                                {countPendingSevereReactions(recentReactions)} severe reactions require attention
                            </Text>
                        </View>
                    )}
                </Card.Content>
            </Card>

            <Card style={styles.card}>
                <Card.Content>
                    <Text style={styles.cardTitle}>Recent Cases</Text>
                    {recentReactions.length > 0 ? (
                        recentReactions.map((reaction) => (
                            <View key={reaction._id} style={styles.reactionItem}>
                                <View style={styles.reactionHeader}>
                                    <View>
                                        <Text style={styles.caseId}>{reaction.user ? `${reaction.user.firstName} ${reaction.user.lastName}` : 'Unknown Patient'}</Text>
                                        <Text style={styles.age}>Age: {reaction.user ? (reaction.user.age ?? 'N/A') : 'N/A'}</Text>
                                        <Text style={styles.sex}>Sex: {reaction.user ? (reaction.user.sex ?? 'N/A') : 'N/A'}</Text>
                                        <Text style={styles.contactNumber}>Contact Number: {reaction.user ? (reaction.user.contactNumber ?? 'N/A') : 'N/A'}</Text>
                                        <Text style={styles.drugName}>Drug Name: {reaction.drugName ?? 'N/A'}</Text>
                                    </View>
                                    <View style={[
                                        styles.severityBadge,
                                        { backgroundColor: getSeverityColor(reaction.severity) }
                                    ]}>
                                        <Text style={styles.severityText}>{reaction.severity}</Text>
                                    </View>
                                </View>
                                <Text style={styles.date}>
                                    Reported: {new Date(reaction.dateReported).toLocaleDateString()}
                                </Text>
                            </View>
                        ))
                    ) : (
                        <Text style={styles.emptyText}>No pending cases</Text>
                    )}
                </Card.Content>
            </Card>

            <Card style={styles.card}>
                <Card.Content>
                    <Text style={styles.cardTitle}>Patient Management</Text>
                    <Button
                        mode="contained"
                        icon="account-group"
                        onPress={() => navigation.navigate('PatientList')}
                        style={styles.button}
                        buttonColor={theme.colors.primary}
                    >
                        View All Patients
                    </Button>
                </Card.Content>
            </Card>
            <Card style={[styles.card, styles.lastCard]}>
                <Card.Content>
                    <Text style={styles.cardTitle}>Communication</Text>
                    <Button
                        mode="contained"
                        icon="chat"
                        onPress={() => navigation.navigate('CounsellorChat')}
                        style={styles.button}
                        buttonColor={theme.colors.primary}
                    >
                        Chat with Patients
                    </Button>
                    <Button
                        mode="contained"
                        icon="doctor"
                        onPress={() => navigation.navigate('CounsellorDoctorChat')}
                        style={styles.button}
                        buttonColor={theme.colors.primary}
                    >
                        Chat with Doctors
                    </Button>
                </Card.Content>
            </Card>

            <Card style={styles.card}>
                <Card.Content>
                    <Text style={styles.cardTitle}>Drug Counselling</Text>
                    <Button
                        mode="contained"
                        icon="pill"
                        onPress={() => navigation.navigate('DrugCounselling')}
                        style={styles.button}
                        buttonColor={theme.colors.primary}
                    >
                        Start Drug Counselling
                    </Button>
                </Card.Content>
            </Card>

            {/* Logout Confirmation Dialog */}
            <Portal>
                <Dialog
                    visible={logoutDialogVisible}
                    onDismiss={hideLogoutDialog}
                    style={[styles.dialogContainer, { width: screenWidth > 500 ? 400 : '85%', alignSelf: 'center' }]}
                >
                    <View style={styles.iconContainer}>
                        <Avatar.Icon
                            size={screenWidth < SCREEN_BREAKPOINT_TABLET ? 56 : 64}
                            icon="logout"
                            color="white"
                            style={{ backgroundColor: paperTheme.colors.error }}
                        />
                    </View>
                    <Dialog.Title style={[
                        styles.dialogTitle,
                        screenWidth < SCREEN_BREAKPOINT_TABLET && { fontSize: 18, marginBottom: theme.spacing.xs }
                    ]}>
                        Confirm Logout
                    </Dialog.Title>
                    <Dialog.Content style={styles.dialogContent}>
                        <Paragraph style={[
                            styles.dialogMessage,
                            screenWidth < SCREEN_BREAKPOINT_TABLET && { fontSize: 14, marginBottom: theme.spacing.sm }
                        ]}>
                            Are you sure you want to log out of the Adverse Drug Reaction Reporting System?
                        </Paragraph>
                    </Dialog.Content>
                    <Dialog.Actions style={[
                        styles.dialogActions,
                        screenWidth < SCREEN_BREAKPOINT_TABLET && {
                            paddingHorizontal: theme.spacing.sm,
                            paddingBottom: theme.spacing.sm
                        }
                    ]}>
                        <Button
                            onPress={hideLogoutDialog}
                            style={[
                                styles.cancelButton,
                                screenWidth < SCREEN_BREAKPOINT_TABLET && {
                                    paddingHorizontal: theme.spacing.sm,
                                    minWidth: 80
                                }
                            ]}
                            labelStyle={[
                                { color: paperTheme.colors.onSurface },
                                screenWidth < SCREEN_BREAKPOINT_TABLET && { fontSize: 12 }
                            ]}
                            mode="outlined"
                        >
                            Cancel
                        </Button>
                        <Button
                            onPress={handleLogoutConfirm}
                            style={[
                                styles.logoutButton,
                                screenWidth < SCREEN_BREAKPOINT_TABLET && {
                                    paddingHorizontal: theme.spacing.sm,
                                    minWidth: 80
                                }
                            ]}
                            mode="contained"
                            labelStyle={screenWidth < SCREEN_BREAKPOINT_TABLET ? { fontSize: 12, color: 'white' } : { color: 'white' }}
                        >
                            Log Out
                        </Button>
                    </Dialog.Actions>
                </Dialog>
            </Portal>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        ...globalStyles.container,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: theme.spacing.lg,
    },
    errorText: {
        fontSize: 16,
        color: theme.colors.error,
        textAlign: 'center',
        marginBottom: theme.spacing.md,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: theme.spacing.lg,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        color: theme.colors.primary,
    },
    subtitle: {
        fontSize: 16,
        color: theme.colors.text,
        marginTop: theme.spacing.xs,
    },
    statsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: theme.spacing.lg,
    },
    statCard: {
        flex: 1,
        margin: theme.spacing.xs,
        padding: theme.spacing.sm,
        alignItems: 'center',
        borderRadius: theme.roundness,
        elevation: 2,
        backgroundColor: theme.colors.surface,
    },
    statNumber: {
        fontSize: 24,
        fontWeight: 'bold',
        color: theme.colors.primary,
    },
    statLabel: {
        fontSize: 12,
        color: theme.colors.text,
        textAlign: 'center',
        marginTop: theme.spacing.xs,
    },
    card: {
        ...globalStyles.card,
        marginBottom: theme.spacing.md,
    },
    lastCard: {
        marginBottom: theme.spacing.xl,
    },
    cardHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: theme.spacing.sm,
    },
    cardTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: theme.colors.text,
    },
    badge: {
        backgroundColor: theme.colors.error,
    },
    button: {
        marginVertical: theme.spacing.xs,
    },
    alertContainer: {
        backgroundColor: theme.colors.errorContainer,
        padding: theme.spacing.sm,
        borderRadius: theme.roundness,
        marginTop: theme.spacing.sm,
    },
    alertText: {
        color: theme.colors.error,
        textAlign: 'center',
        fontWeight: 'bold',
    },
    reactionItem: {
        marginVertical: theme.spacing.sm,
        padding: theme.spacing.sm,
        backgroundColor: theme.colors.background,
        borderRadius: theme.roundness,
    },
    reactionHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: theme.spacing.xs,
    },
    caseId: {
        fontSize: 16,
        fontWeight: 'bold',
        color: theme.colors.text,
    },
    drugName: {
        fontSize: 14,
        color: theme.colors.primary,
    },
    age: {
        fontSize: 14,
        color: theme.colors.text,
    },
    sex: {
        fontSize: 14,
        color: theme.colors.text,
    },
    contactNumber: {
        fontSize: 14,
        color: theme.colors.text,
    },
    severityBadge: {
        paddingHorizontal: theme.spacing.sm,
        paddingVertical: theme.spacing.xs,
        borderRadius: theme.roundness,
    },
    severityText: {
        color: theme.colors.surface,
        fontSize: 12,
        fontWeight: 'bold',
    },
    date: {
        fontSize: 12,
        color: theme.colors.placeholder,
    },
    emptyText: {
        textAlign: 'center',
        color: theme.colors.placeholder,
        fontStyle: 'italic',
        marginVertical: theme.spacing.sm,
    },
    // Logout dialog styles
    dialogContainer: {
        borderRadius: theme.roundness * 2,
        backgroundColor: theme.colors.surface,
        ...globalStyles.shadow,
    },
    dialogTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: theme.colors.primary,
        textAlign: 'center',
        marginBottom: theme.spacing.sm,
    },
    dialogContent: {
        paddingHorizontal: theme.spacing.lg,
        paddingBottom: theme.spacing.md,
    },
    dialogMessage: {
        fontSize: 16,
        color: theme.colors.text,
        textAlign: 'center',
        marginBottom: theme.spacing.md,
    },
    dialogActions: {
        marginTop: theme.spacing.sm,
        paddingHorizontal: theme.spacing.md,
        paddingBottom: theme.spacing.md,
        justifyContent: 'space-between',
    },
    cancelButton: {
        marginRight: theme.spacing.md,
    },
    logoutButton: {
        backgroundColor: theme.colors.error,
    },
    iconContainer: {
        alignItems: 'center',
        marginBottom: theme.spacing.md,
    },
});
