.form-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.drug-counselling-form {
    width: 100%;
    max-width: 600px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

input {
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 90%;
}

button {
    grid-column: span 2;
    padding: 10px;
    background-color: #00A77E;
    color: white;
    border: none;
    margin-top: 10px;
    border-radius: 4px;
    cursor: pointer;
}

button:hover {
    background-color: #007f5c;
}
.button-group {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.button-group button {
    flex: 1; /* optional: makes all buttons equal width */
}
