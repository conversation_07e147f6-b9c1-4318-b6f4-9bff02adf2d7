import React, { useState, useEffect, ChangeEvent, FormEvent, useContext } from 'react';
import { DrugCounsellingContext } from '../context/DrugCounsellingContext';
import { useAuth, API_BASE_URL } from '../context/AuthContext';
import './DrugCounsellingForm.css';

interface DrugCounsellingFormProps {
  existingData: any; // Type this according to your data structure
  onSave: (updated: any) => Promise<void>; // Use proper type here
  onCancel: () => void;
}

const DrugCounsellingForm: React.FC<DrugCounsellingFormProps> = ({ existingData, onSave, onCancel }) => {
  const context = useContext(DrugCounsellingContext);
  if (!context) {
    throw new Error('DrugCounsellingForm must be used within a DrugCounsellingProvider');
  }
  const { addCounsellingData, deleteCounsellingData, updateCounsellingData } = context;
  const { user } = useAuth(); // Accessing current user

  const [formData, setFormData] = useState({
    _id: existingData?._id || '', // Ensure _id is handled
    date: '',
    hospitalNo: '',
    patientName: '',
    ageSex: '',
    address: '',
    contactNo: '',
    diagnosis: '',
    medicalHistory: '',
    medicationHistory: '',
    allergyHistory: '',
    alcoholIntake: '',
    smokingHistory: '',
    prescribedMedicine: '',
    counselingProvided: '',
    averageCounselingTime: '', // Keep this as string for now
    department: '',
    createdBy: '', // New field
    createdAt: '', // New field
    modifiedBy: '', // New field
    modifiedAt: '' // New field
  });

  useEffect(() => {
    if (existingData) {
      setFormData({
        _id: existingData._id || '', // Set _id if it's available
        date: existingData.date || '',
        hospitalNo: existingData.hospitalNo || '',
        patientName: existingData.patientName || '',
        ageSex: existingData.ageSex || '',
        address: existingData.address || '',
        contactNo: existingData.contactNo || '',
        diagnosis: existingData.diagnosis || '',
        medicalHistory: existingData.medicalHistory || '',
        medicationHistory: existingData.medicationHistory || '',
        allergyHistory: existingData.allergyHistory || '',
        alcoholIntake: existingData.alcoholIntake || '',
        smokingHistory: existingData.smokingHistory || '',
        prescribedMedicine: existingData.prescribedMedicine || '',
        counselingProvided: existingData.counselingProvided || '',
        averageCounselingTime: existingData.averageCounselingTime || '',
        department: existingData.department || '',
        createdBy: existingData.createdBy || '',
        createdAt: existingData.createdAt || '',
        modifiedBy: existingData.modifiedBy || '',
        modifiedAt: existingData.modifiedAt || ''
      });
    }
  }, [existingData]);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!formData.hospitalNo || !formData.patientName || !formData.ageSex) {
      console.error("Missing required fields");
      return;
    }

    try {
      const averageCounselingTime = isNaN(Number(formData.averageCounselingTime))
        ? 0
        : Number(formData.averageCounselingTime);

      const updatedFormData = {
        ...formData,
        averageCounselingTime,
        modifiedBy: user ? user.id : "",  // Use standardized user.id property
        createdBy: user ? user.id : "",  // Use standardized user.id property
        createdAt: new Date().toISOString(),  // Provide createdAt field
        modifiedAt: new Date().toISOString(),  // Provide modifiedAt field
      };

      // Remove _id when creating a new entry
      if (!existingData) {
        delete updatedFormData._id;
      }

      console.log('Submitting form with data:', updatedFormData);

      const response = await fetch(
        existingData
          ? `${API_BASE_URL}/api/drugCounselling/${formData._id}`
          : `${API_BASE_URL}/api/drugCounselling`,
        {
          method: existingData ? 'PUT' : 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updatedFormData),
        }
      );

      if (response.ok) {
        const data = await response.json();
        // Update or add the data based on whether it's new or updated
        if (existingData) {
          updateCounsellingData(data); // Use update method for existing data
        } else {
          addCounsellingData(data); // Add new data to context
        }

        // Reset form fields after successful submission
        setFormData({
          _id: '',
          date: '',
          hospitalNo: '',
          patientName: '',
          ageSex: '',
          address: '',
          contactNo: '',
          diagnosis: '',
          medicalHistory: '',
          medicationHistory: '',
          allergyHistory: '',
          alcoholIntake: '',
          smokingHistory: '',
          prescribedMedicine: '',
          counselingProvided: '',
          averageCounselingTime: '',
          department: '',
          createdBy: '',
          createdAt: '',
          modifiedBy: '',
          modifiedAt: ''
        });

        // Close the form
        onCancel();
      } else {
        const errorData = await response.json();
        console.error('Error Response:', errorData); // Log the response error
      }
    } catch (error) {
      console.error('Error submitting form data:', error);
    }
  };

  const handleDelete = async () => {
    if (existingData) {
      try {
        const response = await fetch(`${API_BASE_URL}/api/drugCounselling/${existingData._id}`, {
          method: 'DELETE',
        });
        if (response.ok) {
          deleteCounsellingData(existingData._id); // Remove from context
          onCancel(); // Close the form
        } else {
          console.error('Failed to delete the data');
        }
      } catch (error) {
        console.error('Error deleting data:', error);
      }
    }
  };

  return (
    <div className="form-container">
      <form onSubmit={handleSubmit} className="drug-counselling-form">
        <h2>{existingData ? 'Edit' : 'Add'} Drug Counselling Form</h2>
        <div className="form-grid">
          <input type="date" name="date" value={formData.date} onChange={handleChange} required />
          <input type="text" name="hospitalNo" placeholder="Hospital No" value={formData.hospitalNo} onChange={handleChange} required />
          <input type="text" name="patientName" placeholder="Patient Name" value={formData.patientName} onChange={handleChange} required />
          <input type="text" name="ageSex" placeholder="Age/Sex" value={formData.ageSex} onChange={handleChange} required />
          <input type="text" name="address" placeholder="Address" value={formData.address} onChange={handleChange} required />
          <input type="text" name="contactNo" placeholder="Contact No" value={formData.contactNo} onChange={handleChange} required />
          <input type="text" name="diagnosis" placeholder="Diagnosis/Provisional Diagnosis" value={formData.diagnosis} onChange={handleChange} required />
          <input type="text" name="medicalHistory" placeholder="Any Medical History" value={formData.medicalHistory} onChange={handleChange} />
          <input type="text" name="medicationHistory" placeholder="Any Medication History" value={formData.medicationHistory} onChange={handleChange} />
          <input type="text" name="allergyHistory" placeholder="Any Allergy History" value={formData.allergyHistory} onChange={handleChange} />
          <input type="text" name="alcoholIntake" placeholder="History of Alcohol Intake" value={formData.alcoholIntake} onChange={handleChange} />
          <input type="text" name="smokingHistory" placeholder="History of Smoking" value={formData.smokingHistory} onChange={handleChange} />
          <input type="text" name="prescribedMedicine" placeholder="Prescribed Medicine" value={formData.prescribedMedicine} onChange={handleChange} />
          <input type="text" name="counselingProvided" placeholder="Counseling Provided About" value={formData.counselingProvided} onChange={handleChange} />
          <input type="number" name="averageCounselingTime" placeholder="Average Counseling Time (in min)" value={formData.averageCounselingTime} onChange={handleChange} />
          <input type="text" name="department" placeholder="Department" value={formData.department} onChange={handleChange} />
        </div>
        <div className='button-group'>
        <button type="submit">{existingData ? 'Save Changes' : 'Submit'}</button>
        {existingData && (
          <>
            <button type="button" onClick={handleDelete}>Delete</button>
            <button type="button" onClick={onCancel}>Cancel</button>
          </>
        )}
        {!existingData && <button type="button" onClick={onCancel}>Cancel</button>}
        </div>
      </form>
    </div>
  );
};

export default DrugCounsellingForm;
