export interface Attachment {
  fileType: 'image' | 'document' | 'audio' | 'video' | 'other';
  fileName: string;
  fileSize: number;
  fileData: string; // Base64 encoded data
  mimeType: string;
}

export interface Message {
  id: string;
  text: string;
  timestamp: string;
  senderId: string;
  senderName: string;
  patientId?: string;
  attachment?: Attachment | null;
}

export interface ChatInputProps {
  onSend: (message: string, attachment?: Attachment) => void;
}

export interface ChatMessageProps {
  message: string;
  isCurrentUser: boolean;
  timestamp: string;
  senderName: string;
  status?: 'sent' | 'failed';
  attachment?: Attachment | null;
}

export interface Chat {
  id: string;
  participants: string[];
  messages: Message[];
  lastMessage?: Message;
  createdAt: string;
  updatedAt: string;
}
