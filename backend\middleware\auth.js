const jwt = require('jsonwebtoken');
const User = require('../models/User');

const auth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    if (!token) throw new Error('No token provided');

    console.log('Token received:', token);
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    console.log('Decoded token:', decoded);

    const user = await User.findById(decoded._id); // ✅ Only checks _id
    console.log('User found:', user);

    if (!user) {
      throw new Error('User not found');
    }

    req.user = user;
    req.token = token;
    console.log('Authenticated user:', req.user);
    next();
  } catch (error) {
    console.error('Error in auth middleware:', error.message);
    res.status(401).send({ error: 'Please authenticate.' });
  }
};

module.exports = auth;
