import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Image } from 'react-native';
import { TextInput, Button, Text, ActivityIndicator, Portal, Modal } from 'react-native-paper';
import { theme, styles as globalStyles } from '../../theme';
import { useAuth } from '../../context/AuthContext';

export const LoginScreen = ({ navigation }: any) => {
  const { login, isLoading, error } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [formError, setFormError] = useState<string>('');
  const [passwordVisible, setPasswordVisible] = useState(false);

  // Forgot Password Modal State
  const [showForgotPasswordModal, setShowForgotPasswordModal] = useState(false);
  const [resetEmail, setResetEmail] = useState('');
  const [resetLoading, setResetLoading] = useState(false);
  const [resetError, setResetError] = useState<string>('');
  const [resetSuccess, setResetSuccess] = useState(false);
  const [lastResetTime, setLastResetTime] = useState<number>(0);

  const handleLogin = async () => {
    try {
      setFormError('');

      if (!formData.email || !formData.password) {
        throw new Error('Please fill in all fields');
      }

      await login(formData);

    } catch (err) {
      setFormError(err instanceof Error ? err.message : 'Login failed');
    }
  };

  const handleForgotPassword = async () => {
    try {
      setResetError('');
      setResetSuccess(false);

      // Rate limiting: prevent multiple requests within 60 seconds
      const now = Date.now();
      if (now - lastResetTime < 60000) {
        throw new Error('Please wait before requesting another password reset');
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!resetEmail || !emailRegex.test(resetEmail)) {
        throw new Error('Please enter a valid email address');
      }

      setResetLoading(true);

      // Call the forgot password API
      const response = await fetch(`${process.env.EXPO_PUBLIC_API_URL || 'http://localhost:5000'}/api/forgot-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: resetEmail }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send reset email');
      }

      setResetSuccess(true);
      setLastResetTime(now);
      setResetEmail('');

    } catch (err) {
      setResetError(err instanceof Error ? err.message : 'Failed to send reset email');
    } finally {
      setResetLoading(false);
    }
  };

  const resetForgotPasswordModal = () => {
    setShowForgotPasswordModal(false);
    setResetEmail('');
    setResetError('');
    setResetSuccess(false);
    setResetLoading(false);
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  return (
    <>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.container}>
          <Image source={require('../../../assets/favicon.png')} style={styles.logo} />
        <Text style={styles.title}>Adverse Drug Reaction Reporting System</Text>
        <Text style={styles.note}> </Text>
        <Text style={styles.note}><Text style={{ fontWeight: 'bold' }}>Confidentiality</Text>{'\n'}Any information related to the identities of the reporter and patient will be kept confidential.</Text>
        <Text style={styles.note}><Text style={{ fontWeight: 'bold' }}>What to Report</Text>{'\n'}Any reactions or effects which is noxious(harmful) and/or unintended, and which occurs at doses normally used for prophylaxis, diagnosis, or treatment of a disease, or for the modification of a physiological function.</Text>
        <Text style={styles.note}> </Text>

          {(formError || error) && (
            <Text style={styles.error}>{formError || error}</Text>
          )}

          <TextInput
            label="Email or Username"
            value={formData.email}
            onChangeText={(text) => setFormData({ ...formData, email: text })}
            style={styles.input}
            mode="outlined"
            autoCapitalize="none"
            activeOutlineColor={theme.colors.primary}
            left={<TextInput.Icon icon="account" color={theme.colors.primary} />}
          />

          <TextInput
            label="Password"
            value={formData.password}
            onChangeText={(text) => setFormData({ ...formData, password: text })}
            style={styles.input}
            mode="outlined"
            secureTextEntry={!passwordVisible}
            activeOutlineColor={theme.colors.primary}
            left={<TextInput.Icon icon="lock" color={theme.colors.primary} />}
            right={
              <TextInput.Icon
                icon={passwordVisible ? "eye" : "eye-off"}
                color={theme.colors.primary}
                onPress={() => setPasswordVisible(!passwordVisible)}
              />
            }
          />

          <Button
            mode="contained"
            onPress={handleLogin}
            style={styles.button}
            buttonColor={theme.colors.primary}
          >
            Login
          </Button>

          <Button
            mode="text"
            onPress={() => setShowForgotPasswordModal(true)}
            style={styles.linkButton}
            textColor={theme.colors.primary}
          >
            Forgot Password?
          </Button>

          <Button
            mode="text"
            onPress={() => navigation.navigate('Register')}
            style={styles.linkButton}
            textColor={theme.colors.primary}
          >
            New patient? Create account
          </Button>

        <Text style={styles.note}> </Text>
        <Text style={styles.note}>Pharmacovigilance Unit encourages the reporting of all suspected adverse reactions to drugs and other medicinal substances(including herbal, traditional or alternative remedies)</Text>
        <Text style={styles.note}>Please report even you are not certain the product caused the adverse reaction</Text>
        </View>
      </ScrollView>

      {/* Forgot Password Modal */}
      <Portal>
        <Modal
          visible={showForgotPasswordModal}
          onDismiss={resetForgotPasswordModal}
          contentContainerStyle={styles.modalContainer}
        >
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Reset Password</Text>
            <Text style={styles.modalSubtitle}>
              Enter your email address and we'll send you instructions to reset your password.
            </Text>

            {resetError && (
              <Text style={styles.error}>{resetError}</Text>
            )}

            {resetSuccess && (
              <Text style={styles.successMessage}>
                If an account with that email exists, we've sent password reset instructions.
              </Text>
            )}

            {!resetSuccess && (
              <>
                <TextInput
                  label="Email Address"
                  value={resetEmail}
                  onChangeText={setResetEmail}
                  style={styles.input}
                  mode="outlined"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  activeOutlineColor={theme.colors.primary}
                  left={<TextInput.Icon icon="email" color={theme.colors.primary} />}
                  disabled={resetLoading}
                />

                <Button
                  mode="contained"
                  onPress={handleForgotPassword}
                  style={styles.button}
                  buttonColor={theme.colors.primary}
                  loading={resetLoading}
                  disabled={resetLoading}
                >
                  {resetLoading ? 'Sending...' : 'Send Reset Instructions'}
                </Button>
              </>
            )}

            <Button
              mode="text"
              onPress={resetForgotPasswordModal}
              style={styles.linkButton}
              textColor={theme.colors.primary}
              disabled={resetLoading}
            >
              {resetSuccess ? 'Close' : 'Cancel'}
            </Button>
          </View>
        </Modal>
      </Portal>
    </>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
  },
  container: {
    ...globalStyles.container,
    justifyContent: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    color: theme.colors.primary,
    marginBottom: theme.spacing.lg,
  },
  input: {
    ...globalStyles.input,
  },
  button: {
    ...globalStyles.button,
  },
  linkButton: {
    marginTop: theme.spacing.sm,
  },
  error: {
    ...globalStyles.error,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  note: {
    fontSize: 16,
    color: theme.colors.placeholder,
    textAlign: 'center',
    marginTop: theme.spacing.md,
    fontStyle: 'italic',
  },
  note_text: {
    fontSize: 16,
    color: theme.colors.placeholder,
    marginTop: theme.spacing.md,
    fontStyle: 'italic',
  },
  logo: {
    width: 300,
    height: 200,
    alignSelf: 'center',
    marginBottom: theme.spacing.lg,
  },
  modalContainer: {
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.lg,
    margin: theme.spacing.lg,
    borderRadius: 8,
    maxWidth: 400,
    alignSelf: 'center',
  },
  modalContent: {
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  modalSubtitle: {
    fontSize: 14,
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
    lineHeight: 20,
  },
  successMessage: {
    fontSize: 14,
    color: theme.colors.primary,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
    padding: theme.spacing.md,
    backgroundColor: theme.colors.primaryContainer,
    borderRadius: 4,
    lineHeight: 20,
  },
});
