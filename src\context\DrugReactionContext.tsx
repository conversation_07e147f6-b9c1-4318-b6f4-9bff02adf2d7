import React, { createContext, useContext, useState, useCallback } from 'react';
import type {
  DrugReaction,
  ReactionReport,
  ReactionUpdate,
  ReactionStats,
  ReactionFilters
} from '../types/drugReaction';
import { useAuth, API_BASE_URL } from './AuthContext';
import axios from 'axios';

interface DrugReactionContextType {
  reactions: DrugReaction[];
  isLoading: boolean;
  error: string | null;
  stats: ReactionStats;
  recentReport: DrugReaction | null;
  submitReaction: (report: ReactionReport) => Promise<void>;
  updateReaction: (update: ReactionUpdate) => Promise<void>;
  getReactions: (filters?: ReactionFilters) => Promise<DrugReaction[]>;
  getReactionById: (_id: string) => Promise<DrugReaction | null>;
  getStats: () => Promise<ReactionStats>;
  getTotalStats: () => Promise<ReactionStats>;
}

const DrugReactionContext = createContext<DrugReactionContextType | undefined>(undefined);

export const DrugReactionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [reactions, setReactions] = useState<DrugReaction[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<ReactionStats>({
    total: 0,
    pending: 0,
    resolved: 0,
    bySeverity: {
      mild: 0,
      moderate: 0,
      severe: 0,
    },
  });
  const [recentReport, setRecentReport] = useState<DrugReaction | null>(null);

  const getStats = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      const response = await axios.get(`${API_BASE_URL}/api/drug-reactions/user/${user.id}/stats`);
      const fetchedStats = response.data;

      setStats({
        total: fetchedStats.total,
        pending: fetchedStats.pending,
        resolved: fetchedStats.resolved,
        bySeverity: {
          mild: fetchedStats.bySeverity?.mild || 0,
          moderate: fetchedStats.bySeverity?.moderate || 0,
          severe: fetchedStats.bySeverity?.severe || 0,
        },
      });

      if (fetchedStats.recentReport) {
        setRecentReport(fetchedStats.recentReport);
      }

      return fetchedStats;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch statistics');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [user?.id]);

  const getTotalStats = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await axios.get(`${API_BASE_URL}/api/drug-reactions/stats/total`);
      const fetchedStats = response.data;

      setStats({
        total: fetchedStats.total,
        pending: fetchedStats.pending,
        resolved: fetchedStats.resolved,
        bySeverity: {
          mild: fetchedStats.bySeverity?.mild || 0,
          moderate: fetchedStats.bySeverity?.moderate || 0,
          severe: fetchedStats.bySeverity?.severe || 0,
        },
      });

      return fetchedStats;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch total statistics');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const submitReaction = useCallback(async (report: ReactionReport) => {
    try {
      setIsLoading(true);
      setError(null);

      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      const response = await axios.post(`${API_BASE_URL}/api/drug-reactions`, {
        ...report,
        user: user.id,
      });

      const newReaction = response.data.data;
      setReactions(prev => [...prev, newReaction]);

      await getStats();
    } catch (err) {
      console.error('Error submitting reaction:', err);
      setError(err instanceof Error ? err.message : 'Failed to submit reaction report');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, getStats]);

  const updateReaction = useCallback(async (update: ReactionUpdate) => {
    try {
      setIsLoading(true);
      setError(null);

      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      await axios.put(`${API_BASE_URL}/api/drug-reactions/${update.id}`, update);

      setReactions(prev => prev.map(reaction =>
        reaction._id === update.id
          ? {
              ...reaction,
              ...update,
              updatedAt: new Date().toISOString(),
            }
          : reaction
      ));

      await getStats();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update reaction');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, getStats]);

  const getReactions = useCallback(async (filters?: ReactionFilters) => {
    try {
      setIsLoading(true);
      setError(null);

      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      const response = await axios.get(`${API_BASE_URL}/api/drug-reactions/user/${user.id}`);
      let fetchedReactions = response.data;

      if (filters) {
        if (filters.status) {
          fetchedReactions = fetchedReactions.filter((r: DrugReaction) => r.status === filters.status);
        }
        if (filters.severity) {
          fetchedReactions = fetchedReactions.filter((r: DrugReaction) => r.severity === filters.severity);
        }
        if (filters.assignedCounsellorId) {
          fetchedReactions = fetchedReactions.filter((r: DrugReaction) => r.assignedCounsellorId === filters.assignedCounsellorId);
        }
        if (filters.dateRange) {
          fetchedReactions = fetchedReactions.filter((r: DrugReaction) => {
            const reportDate = new Date(r.dateReported);
            const start = new Date(filters.dateRange!.start);
            const end = new Date(filters.dateRange!.end);
            return reportDate >= start && reportDate <= end;
          });
        }
      }

      setReactions(fetchedReactions);
      return fetchedReactions;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch reactions');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [user?.id]);

  const getReactionById = useCallback(async (_id: string) => {
    try {
      setIsLoading(true);
      setError(null);

      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      const response = await axios.get(`${API_BASE_URL}/api/drug-reactions/${_id}`);
      return response.data;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch reaction');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [user?.id]);

  return (
    <DrugReactionContext.Provider
      value={{
        reactions,
        isLoading,
        error,
        stats,
        recentReport,
        submitReaction,
        updateReaction,
        getReactions,
        getReactionById,
        getStats,
        getTotalStats,
      }}
    >
      {children}
    </DrugReactionContext.Provider>
  );
};

export const useDrugReaction = () => {
  const context = useContext(DrugReactionContext);
  if (context === undefined) {
    throw new Error('useDrugReaction must be used within a DrugReactionProvider');
  }
  return context;
};