import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, FlatList, Text, TextInput, Dimensions, TouchableOpacity } from 'react-native';
import { useTheme, IconButton, Card } from 'react-native-paper';
import axios from 'axios';
import { io } from 'socket.io-client';
import { useAuth, API_BASE_URL } from '../../context/AuthContext';
import ChatMessage from '../../components/ChatMessage';
import ChatInput from '../../components/ChatInput';
import { Message, Attachment } from '../../types/chat';
import { transformPatients } from '../../utils/dataTransformers';

const PatientChatScreen: React.FC = () => {
  const theme = useTheme();
  const { user } = useAuth();

  // Messages, socket, and counsellor
  const [messages, setMessages] = useState<Message[]>([]);
  const [socket, setSocket] = useState<any>(null);
  const [counsellor, setCounsellor] = useState<any>(null);
  const [availableCounsellors, setAvailableCounsellors] = useState<any[]>([]);

  // Scroll logic
  const [isUserAtBottom, setIsUserAtBottom] = useState(true);
  const flatListRef = useRef<FlatList>(null);

  // Screen size for responsiveness
  const screenWidth = Dimensions.get('window').width;
  const isMobile = screenWidth < 600;

  // Search functionality
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);

  // Filter messages based on searchTerm
  const displayedMessages = messages.filter((msg) =>
    msg.text.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Fetch available counsellors
  useEffect(() => {
    const fetchCounsellors = async () => {
      try {
        const response = await axios.get(`${API_BASE_URL}/api/staff/counsellors`);
        // Transform MongoDB documents to standardized objects with 'id' property
        const transformedCounsellors = transformPatients(response.data);
        setAvailableCounsellors(transformedCounsellors);
      } catch (error) {
        console.error('Error fetching counsellors:', error);
      }
    };
    fetchCounsellors();
  }, []);

  // Socket & Counsellor Setup
  useEffect(() => {
    const newSocket = io(API_BASE_URL);
    setSocket(newSocket);

    return () => {
      if (newSocket) {
        newSocket.disconnect();
      }
    };
  }, []);

  // Join room & receive messages
  useEffect(() => {
    if (!socket || !user?.id) return;

    socket.emit('join-room', user.id);

    socket.on('receive-message', (message: any) => {
      setMessages((prev) => {
        // Avoid duplicates
        if (!prev.some((m) => m.id === message.id)) {
          return [...prev, message];
        }
        return prev;
      });

      if (isUserAtBottom) {
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 100);
      }
    });

    return () => {
      socket.off('receive-message');
    };
  }, [socket, user, isUserAtBottom]);

  // Fetch chat history once counsellor is known
  useEffect(() => {
    if (counsellor?.id) {
      fetchChatHistory();
    }
  }, [counsellor]);

  const fetchChatHistory = async () => {
    if (!user?.id || !counsellor?.id) return;
    try {
      const response = await axios.get(
        `${API_BASE_URL}/api/chat/history/${user.id}/${counsellor.id}`
      );

      const formatted = response.data.map((msg: any) => ({
        id: msg._id,
        text: msg.content,
        timestamp: msg.timestamp,
        senderId: msg.senderId._id,
        senderName: msg.senderId.name,
        attachment: msg.attachment || null
      }));

      // Reverse so latest is at bottom
      setMessages(formatted.reverse());

      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: false });
      }, 100);
    } catch (error) {
      console.error('Error fetching chat history:', error);
    }
  };

  // Track scroll to see if user is at bottom
  const handleScroll = (event: any) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const atBottom = layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;
    setIsUserAtBottom(atBottom);
  };

  // Handle send relies on socket for new messages
  const handleSend = async (message: string, attachment?: Attachment) => {
    if (!user?.id || !socket || !counsellor?.id) return;

    const messageData: any = {
      sender: user.id,
      receiver: counsellor.id,
      content: message
    };

    // Add attachment if present
    if (attachment) {
      messageData.attachment = attachment;
    }

    try {
      const response = await axios.post(`${API_BASE_URL}/api/chat/send`, messageData);

      // Emit to socket instead of updating local state => avoid duplicates
      socket.emit('send-message', {
        id: response.data._id,
        text: message,
        attachment: attachment || null,
        timestamp: new Date().toISOString(),
        senderId: user.id,
        senderName: user.name
      });
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  // Counsellor selection handler
  const handleCounsellorSelect = (selectedCounsellor: any) => {
    setCounsellor(selectedCounsellor);
    // Clear previous messages when switching counsellors
    setMessages([]);
  };

  // Layout & Rendering
  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {isMobile ? (
        counsellor ? (
          <View style={styles.fullWidthChatContainer}>
            <View style={styles.chatHeader}>
              <IconButton icon="arrow-left" onPress={() => setCounsellor(null)} size={24} />
              <Text style={styles.counsellorName}>{counsellor?.name || 'Drug Counsellor'}</Text>

              <View style={styles.searchContainer}>
                {isSearchExpanded ? (
                  <View style={styles.searchInputContainer}>
                    <TextInput
                      style={styles.searchInput}
                      placeholder="Search messages..."
                      value={searchTerm}
                      onChangeText={setSearchTerm}
                      autoFocus
                    />
                    <IconButton
                      icon="close"
                      onPress={() => {
                        setSearchTerm('');
                        setIsSearchExpanded(false);
                      }}
                      size={20}
                    />
                  </View>
                ) : (
                  <IconButton
                    icon="magnify"
                    onPress={() => setIsSearchExpanded(true)}
                    size={24}
                  />
                )}
              </View>
            </View>

            <FlatList
              ref={flatListRef}
              data={displayedMessages}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <ChatMessage
                  message={item.text}
                  isCurrentUser={item.senderId === user?.id}
                  timestamp={item.timestamp}
                  senderName={item.senderName}
                  attachment={item.attachment}
                />
              )}
              contentContainerStyle={styles.messagesContainer}
              onScroll={handleScroll}
              onEndReachedThreshold={0.5}
            />
            <ChatInput onSend={handleSend} />
          </View>
        ) : (
          <View style={styles.fullWidthCounsellorList}>
            <View style={styles.counsellorSearchContainer}>
              <TextInput
                style={styles.counsellorSearchInput}
                placeholder="Search counsellors..."
                value={searchTerm}
                onChangeText={setSearchTerm}
              />
            </View>

            <FlatList
              data={availableCounsellors}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.counsellorItem,
                    counsellor?.id === item.id && styles.selectedCounsellor
                  ]}
                  onPress={() => handleCounsellorSelect(item)}
                >
                  <View>
                    <Text style={styles.counsellorName}>{item.name}</Text>
                    {item.department && (
                      <Text style={styles.counsellorDepartment}>{item.department}</Text>
                    )}
                  </View>
                </TouchableOpacity>
              )}
              contentContainerStyle={styles.counsellorList}
            />
          </View>
        )
      ) : (
        <View style={styles.desktopLayout}>
          <View style={styles.leftPanel}>
            <View style={styles.counsellorSearchContainer}>
              <TextInput
                style={styles.counsellorSearchInput}
                placeholder="Search counsellors..."
                value={searchTerm}
                onChangeText={setSearchTerm}
              />
            </View>

            <FlatList
              data={availableCounsellors}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.counsellorItem,
                    counsellor?.id === item.id && styles.selectedCounsellor
                  ]}
                  onPress={() => handleCounsellorSelect(item)}
                >
                  <View>
                    <Text style={styles.counsellorName}>{item.name}</Text>
                    {item.department && (
                      <Text style={styles.counsellorDepartment}>{item.department}</Text>
                    )}
                  </View>
                </TouchableOpacity>
              )}
              contentContainerStyle={styles.counsellorList}
            />
          </View>

          <View style={styles.rightPanel}>
            {counsellor ? (
              <View style={styles.chatContainer}>
                <View style={styles.chatHeader}>
                  <Text style={styles.counsellorName}>{counsellor?.name || 'Drug Counsellor'}</Text>

                  <View style={styles.searchContainer}>
                    {isSearchExpanded ? (
                      <View style={styles.searchInputContainer}>
                        <TextInput
                          style={styles.searchInput}
                          placeholder="Search messages..."
                          value={searchTerm}
                          onChangeText={setSearchTerm}
                          autoFocus
                        />
                        <IconButton
                          icon="close"
                          onPress={() => {
                            setSearchTerm('');
                            setIsSearchExpanded(false);
                          }}
                          size={20}
                        />
                      </View>
                    ) : (
                      <IconButton
                        icon="magnify"
                        onPress={() => setIsSearchExpanded(true)}
                        size={24}
                      />
                    )}
                  </View>
                </View>

                <FlatList
                  ref={flatListRef}
                  data={displayedMessages}
                  keyExtractor={(item) => item.id}
                  renderItem={({ item }) => (
                    <ChatMessage
                      message={item.text}
                      isCurrentUser={item.senderId === user?.id}
                      timestamp={item.timestamp}
                      senderName={item.senderName}
                      attachment={item.attachment}
                    />
                  )}
                  contentContainerStyle={styles.messagesContainer}
                  onScroll={handleScroll}
                  onEndReachedThreshold={0.5}
                />
                <ChatInput onSend={handleSend} />
              </View>
            ) : (
              <View style={styles.noCounsellorSelected}>
                <Text>Select a counsellor to start chatting</Text>
              </View>
            )}
          </View>
        </View>
      )}
    </View>
  );
};

// Styles
const styles = StyleSheet.create({
  container: { flex: 1 },
  chatContainer: { flex: 1 },
  fullWidthChatContainer: { flex: 1, width: '100%' },
  messagesContainer: { padding: 16 },

  chatHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
    backgroundColor: '#fff',
    justifyContent: 'space-between'
  },
  counsellorName: { fontSize: 16, fontWeight: 'bold' },

  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 4
  },
  searchInput: {
    flex: 1,
    padding: 8,
    marginRight: 4,
    minWidth: 150
  },

  emptyChatState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center'
  },
  emptyChatText: { fontSize: 16, color: '#666' },

  fullWidthCounsellorList: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff'
  },
  leftPanel: {
    width: '30%',
    borderRightWidth: 1,
    borderRightColor: '#e1e1e1',
    backgroundColor: '#fff'
  },
  rightPanel: {
    flex: 1,
    backgroundColor: '#fff'
  },
  counsellorSearchContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1'
  },
  counsellorSearchInput: {
    padding: 8,
    borderWidth: 1,
    borderColor: '#e1e1e1',
    borderRadius: 8
  },
  counsellorList: {
    padding: 8
  },
  counsellorItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    backgroundColor: '#fff'
  },
  selectedCounsellor: {
    backgroundColor: '#f0f0f0'
  },
  counsellorDepartment: {
    fontSize: 14,
    color: '#666',
    marginTop: 4
  },
  noCounsellorSelected: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  desktopLayout: {
    flex: 1,
    flexDirection: 'row'
  }
});

export default PatientChatScreen;
