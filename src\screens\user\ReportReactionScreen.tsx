import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Image, TouchableOpacity } from 'react-native';
import { launchImageLibrary, ImagePickerResponse } from 'react-native-image-picker';
import {
  TextInput,
  Button,
  Text,
  SegmentedButtons,
  Chip,
  Portal,
  Modal,
  ActivityIndicator,
  List,
  IconButton,
  Checkbox
} from 'react-native-paper';
import { DatePickerModal } from 'react-native-paper-dates';
import { theme, styles as globalStyles } from '../../theme';
import axios from 'axios';
import { useAuth, API_BASE_URL } from '../../context/AuthContext';
import type { ReactionSeverity } from '../../types/drugReaction';

const COMMON_SYMPTOMS = [
  'Rash',
  'Vomiting',
  'Fever',
  'Diarrhea',
  'Vertigo',
  'Acne',
  'Dizziness',
  'Swelling of Lips',
  'Rigors and Chills',
  'Bradycardia',
  'Thrombocytopenia',
  'Convulsion Disorder',
  'Abdominal Pain',
  'Nausea',
  'Headache',
  'Itching',
  'Swelling',
  'Breathing Difficulty',
];

export const ReportReactionScreen = ({ navigation }: any) => {
  const { user } = useAuth();
  const [formError, setFormError] = useState<string>('');
  const [showSymptomModal, setShowSymptomModal] = useState(false);
  const [customSymptom, setCustomSymptom] = useState('');
  const [showDatePickerStarted, setShowDatePickerStarted] = useState(false);
  const [showDatePickerStopped, setShowDatePickerStopped] = useState(false);

  const [formData, setFormData] = useState({
    drugName: '',
    reactionDetails: '',
    severity: 'moderate' as ReactionSeverity,
    symptoms: [] as string[],
    dosageForm: '',
    routeOfAdministration: '',
    dailyDose: '',
    dateStarted: new Date(),
    dateStopped: new Date(),
    indications: '',
    outcomes: [] as string[],
    outcomesOthers: '',
    image: '',
  });

  const [outcomes, setOutcomes] = useState<string[]>([]);
  const [outcomesOthers, setOthers] = useState<string>('');

  const handleAddSymptom = (symptom: string) => {
    if (!formData.symptoms.includes(symptom)) {
      setFormData(prev => ({
        ...prev,
        symptoms: [...prev.symptoms, symptom],
      }));
    }
  };

  const handleRemoveSymptom = (symptom: string) => {
    setFormData(prev => ({
      ...prev,
      symptoms: prev.symptoms.filter(s => s !== symptom),
    }));
  };

  const handleAddCustomSymptom = () => {
    if (customSymptom.trim() && !formData.symptoms.includes(customSymptom.trim())) {
      handleAddSymptom(customSymptom.trim());
      setCustomSymptom('');
    }
  };

  const handleSubmit = async () => {
    try {
      setFormError('');

      if (!formData.drugName.trim()) {
        throw new Error('Please enter the drug name');
      }

      if (!formData.reactionDetails.trim()) {
        throw new Error('Please describe the reaction');
      }

      if (formData.symptoms.length === 0) {
        throw new Error('Please select at least one symptom');
      }

      if (!user) {
        throw new Error('User not authenticated');
      }

      const response = await axios.post(`${API_BASE_URL}/api/drug-reactions`, {
        user: user.id, // Use user.id instead of the entire user object
        drugName: formData.drugName,
        reactionDetails: formData.reactionDetails,
        symptoms: formData.symptoms,
        severity: formData.severity,
        dosageForm: formData.dosageForm,
        routeOfAdministration: formData.routeOfAdministration,
        dailyDose: formData.dailyDose,
        dateStarted: formData.dateStarted,
        dateStopped: formData.dateStopped,
        indications: formData.indications,
        outcomes: outcomes,
        outcomesOthers: outcomesOthers,
        image: formData.image,
    });

      navigation.goBack();
    } catch (err) {
      setFormError(err instanceof Error ? err.message : 'Failed to submit report');
    }
  };

  return (
    <ScrollView
      style={styles.container}
      keyboardShouldPersistTaps="handled"
    >
      <Text style={styles.title}>Report Drug Reaction</Text>

      {formError && (
        <Text style={styles.error}>{formError}</Text>
      )}

      <TextInput
        label="Drug Name"
        value={formData.drugName}
        onChangeText={(text) => setFormData({ ...formData, drugName: text })}
        style={styles.input}
        mode="outlined"
      />
      <Text style={styles.label}>Severity</Text>

      <SegmentedButtons
        value={formData.severity}
        onValueChange={(value) =>
          setFormData({ ...formData, severity: value as ReactionSeverity })
        }
        buttons={[
          { value: 'mild', label: 'Mild' },
          { value: 'moderate', label: 'Moderate' },
          { value: 'severe', label: 'Severe' },
        ]}
        style={styles.segmentedButtons}
      />

      <TextInput
        label="Dosage Form"
        value={formData.dosageForm}
        onChangeText={(text) => setFormData({ ...formData, dosageForm: text })}
        style={styles.input}
        mode="outlined"
      />

      <TextInput
        label="Route of Administration"
        value={formData.routeOfAdministration}
        onChangeText={(text) => setFormData({ ...formData, routeOfAdministration: text })}
        style={styles.input}
        mode="outlined"
      />

      <TextInput
        label="Daily Dose"
        value={formData.dailyDose}
        onChangeText={(text) => setFormData({ ...formData, dailyDose: text })}
        style={styles.input}
        mode="outlined"
      />

      <TextInput
        label="Indications"
        value={formData.indications}
        onChangeText={(text) => setFormData({ ...formData, indications: text })}
        style={styles.input}
        mode="outlined"
      />

      <Text style={styles.label}>Date Started</Text>
      <TextInput
        label="Date Started"
        value={formData.dateStarted.toLocaleDateString()}
        onFocus={() => setShowDatePickerStarted(true)}
        style={styles.input}
        mode="outlined"
        left={<TextInput.Icon icon="calendar" />}
      />
      <DatePickerModal
        mode="single"
        visible={showDatePickerStarted}
        onDismiss={() => setShowDatePickerStarted(false)}
        date={formData.dateStarted}
        onConfirm={({ date }) => {
          if (date) {
            console.log("Selected Date Started:", date);
            setFormData({ ...formData, dateStarted: date });
          }
          setShowDatePickerStarted(false);
        }}
        locale="en"
      />

      <Text style={styles.label}>Date Stopped</Text>
      <TextInput
        label="Date Stopped"
        value={formData.dateStopped.toLocaleDateString()}
        onFocus={() => setShowDatePickerStopped(true)}
        style={styles.input}
        mode="outlined"
        left={<TextInput.Icon icon="calendar" />}
      />
      <DatePickerModal
        mode="single"
        visible={showDatePickerStopped}
        onDismiss={() => setShowDatePickerStopped(false)}
        date={formData.dateStopped}
        onConfirm={({ date }) => {
          if (date) {
            setFormData({ ...formData, dateStopped: date });
          }
          setShowDatePickerStopped(false);
        }}
        locale="en"
      />

      <Text style={styles.label}>Upload Image (If you suspect something is causing you allergy)</Text>
      <View style={styles.imageUploadSection}>
        <Button
          mode="outlined"
          icon="camera"
          onPress={async () => {
            try {
              const result = await launchImageLibrary({
                mediaType: 'photo',
                includeBase64: true,
                maxWidth: 800,
                maxHeight: 600,
                quality: 0.8,
              });

              const base64Image = result.assets?.[0]?.base64;
              if (!result.didCancel && base64Image) {
                setFormData(prev => ({
                  ...prev,
                  image: base64Image
                }));
              }
            } catch (error) {
              console.error('Image picker error:', error);
              setFormError('Failed to upload image');
            }
          }}
          style={styles.uploadButton}
        >
          {formData.image ? 'Change Image' : 'Upload Image'}
        </Button>
        {formData.image && (
          <View style={styles.imagePreviewContainer}>
            <Image
              source={{ uri: `data:image/jpeg;base64,${formData.image}` }}
              style={styles.imagePreview}
            />
            <IconButton
              icon="close"
              size={20}
              onPress={() => setFormData(prev => ({ ...prev, image: '' }))}
              style={styles.removeImageButton}
            />
          </View>
        )}
      </View>

      <TextInput
        label="Describe the Reaction"
        value={formData.reactionDetails}
        onChangeText={(text) => setFormData({ ...formData, reactionDetails: text })}
        style={styles.input}
        mode="outlined"
        multiline
        numberOfLines={4}
      />

      <Text style={styles.label}>Outcomes of Reactions:</Text>
      <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
        {['Fatal', 'Recovered', 'Recovering', 'Continuing', 'Unknown', 'Others'].map((outcome) => (
          <View key={outcome} style={styles.checkboxContainer}>
            <Checkbox
              status={outcomes.includes(outcome) ? 'checked' : 'unchecked'}
              onPress={() => {
                if (outcome === 'Others') {
                  if (outcomes.includes(outcome)) {
                    setOutcomes(outcomes.filter(o => o !== outcome));
                    setOthers('');
                  } else {
                    setOutcomes([...outcomes, outcome]);
                  }
                } else {
                  if (outcomes.includes(outcome)) {
                    setOutcomes(outcomes.filter(o => o !== outcome));
                  } else {
                    setOutcomes([...outcomes, outcome]);
                  }
                }
              }}
            />
            <Text>{outcome}</Text>
          </View>
        ))}
        {outcomes.includes('Others') && (
          <TextInput
            label="Please Specify"
            value={outcomesOthers}
            onChangeText={setOthers}
            style={styles.input}
            mode="outlined"
          />
        )}
      </View>

      <View style={styles.symptomsSection}>
        <View style={styles.symptomsHeader}><Text style={styles.label}>Symptoms</Text>
          <Button
            mode="contained-tonal"
            onPress={() => setShowSymptomModal(true)}
            style={styles.addButton}
          >
            Add Symptom
          </Button>
        </View>

        <View style={styles.chipContainer}>
          {formData.symptoms.map((symptom) => (
            <Chip
              key={symptom}
              onClose={() => handleRemoveSymptom(symptom)}
              style={styles.chip}
            >
              {symptom}
            </Chip>
          ))}
        </View>
      </View>

      <Button
        mode="contained"
        onPress={handleSubmit}
        style={styles.submitButton}
      >
        Submit Report
      </Button>

      <Portal>
        <Modal
          visible={showSymptomModal}
          onDismiss={() => setShowSymptomModal(false)}
          contentContainerStyle={styles.modalContainer}
        ><Text style={styles.modalTitle}>Select Symptoms</Text>
          <View style={styles.customSymptomInput}>
            <TextInput
              label="Add Custom Symptom"
              value={customSymptom}
              onChangeText={setCustomSymptom}
              style={{ flex: 1 }}
              mode="outlined"
            />
            <Button
              mode="contained"
              onPress={handleAddCustomSymptom}
              style={{ marginLeft: theme.spacing.sm }}
              disabled={!customSymptom.trim()}
            >
              Add
            </Button>
          </View>

          <ScrollView style={styles.symptomsList}>
            {COMMON_SYMPTOMS.map((symptom) => (
              <List.Item
                key={symptom}
                title={symptom}
                onPress={() => handleAddSymptom(symptom)}
                left={props => <List.Icon {...props} icon="plus" />}
                style={formData.symptoms.includes(symptom) ? styles.selectedSymptom : undefined}
              />
            ))}
          </ScrollView>

          <Button
            mode="outlined"
            onPress={() => setShowSymptomModal(false)}
            style={styles.modalButton}
          >
            Done
          </Button>
        </Modal>
      </Portal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  imageUploadSection: {
    marginBottom: theme.spacing.md,
  },
  uploadButton: {
    marginBottom: theme.spacing.sm,
  },
  imagePreviewContainer: {
    position: 'relative',
    alignItems: 'center',
    marginTop: theme.spacing.sm,
  },
  imagePreview: {
    width: 200,
    height: 150,
    borderRadius: theme.roundness,
  },
  removeImageButton: {
    position: 'absolute',
    top: -10,
    right: -10,
    backgroundColor: theme.colors.error,
  },
  container: {
    ...globalStyles.container,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: theme.spacing.lg,
  },
  input: {
    marginBottom: theme.spacing.md,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  segmentedButtons: {
    marginBottom: theme.spacing.md,
  },
  symptomsSection: {
    marginBottom: theme.spacing.md,
  },
  symptomsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  addButton: {
    marginLeft: theme.spacing.sm,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.sm,
  },
  chip: {
    marginBottom: theme.spacing.xs,
  },
  submitButton: {
    marginVertical: theme.spacing.lg,
  },
  modalContainer: {
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.lg,
    margin: theme.spacing.md,
    borderRadius: theme.roundness,
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: theme.spacing.md,
    color: theme.colors.primary,
  },
  customSymptomInput: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  symptomsList: {
    maxHeight: 400,
  },
  selectedSymptom: {
    backgroundColor: theme.colors.primaryContainer,
  },
  modalButton: {
    marginTop: theme.spacing.md,
  },
  checkboxContainer: {
  flexDirection: 'row',
  alignItems: 'center',
  marginBottom: theme.spacing.sm,
},
  error: {
    color: theme.colors.error,
    marginBottom: theme.spacing.md,
  },
});

export default ReportReactionScreen;
