import React, { createContext, useState, ReactNode } from 'react';

export interface CounsellingData {
    _id: string; 
    date: string;
    hospitalNo: string;
    patientName: string;
    ageSex: string;
    address: string;
    contactNo: string;
    diagnosis: string;
    medicalHistory?: string;
    medicationHistory?: string;
    allergyHistory?: string;
    alcoholIntake?: string;
    smokingHistory?: string;
    prescribedMedicine?: string;
    counselingProvided?: string;
    averageCounselingTime?: number;
    department?: string;
}

interface DrugCounsellingContextType {
    counsellingData: CounsellingData[];
    addCounsellingData: (data: CounsellingData) => void;
    clearCounsellingData: () => void;
    deleteCounsellingData: (id: string) => void;
    updateCounsellingData: (updatedData: CounsellingData) => void;
}

export const DrugCounsellingContext = createContext<DrugCounsellingContextType | undefined>(undefined);

export const DrugCounsellingProvider = ({ children }: { children: ReactNode }) => {
    const [counsellingData, setCounsellingData] = useState<CounsellingData[]>([]);

    const clearCounsellingData = () => {
        setCounsellingData([]);
    };

    const addCounsellingData = (data: CounsellingData) => {
        setCounsellingData(prevData => [...prevData, data]);
    };

    const deleteCounsellingData = (id: string) => {
        setCounsellingData(prevData => prevData.filter(item => item._id !== id));
    };

    const updateCounsellingData = (updatedData: CounsellingData) => {
        setCounsellingData(prevData =>
            prevData.map(item =>
                item._id === updatedData._id ? updatedData : item
            )
        );
    };

    return (
        <DrugCounsellingContext.Provider value={{ 
            counsellingData, 
            addCounsellingData, 
            clearCounsellingData,
            deleteCounsellingData,
            updateCounsellingData 
        }}>
            {children}
        </DrugCounsellingContext.Provider>
    );
};
