{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.7.9", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "googleapis": "^148.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.9.2", "nodemailer": "^6.10.1", "socket.io": "^4.8.1"}}