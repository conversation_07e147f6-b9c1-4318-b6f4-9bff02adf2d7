export type ReactionSeverity = 'mild' | 'moderate' | 'severe';
export type ReactionStatus = 'pending' | 'resolved';

export interface DrugReaction {
  user: {
    _id: string;
    firstName: string;
    lastName: string;
    age: number;
    sex: string;
    contactNumber: string;  
  };
  _id: string;
  drugName: string;
  reactionDetails: string; 
  severity: ReactionSeverity;
  symptoms: string[];
  dateReported: string;
  status: ReactionStatus;
  resolvedBy?: string;  // ID of the counsellor who resolved it
  resolvedAt?: string;  // ISO date string of when it was resolved
  assignedCounsellorId?: string;
  assignedCounsellorName?: string;
  notes?: string;
  image?: File[]; 
  updatedAt: string;
  dateStarted?: string;
  dateStopped?: string;
  dailyDose?: string;
  routeOfAdministration?: string;
  dosageForm?: string;
  outcomes?: string;
  outcomesOthers?: string;
  treatmentGiven?: string;
  timeAfterExposure?: string;
  hospitalized?: boolean;
  allergyCard?: boolean;
  discontinuation?: boolean;
  resolvedById?:string;
}

export interface ReactionReport {
  drugName: string;
  reactionDetails: string;  
  severity: ReactionSeverity;
  symptoms: string[];
  image?: File[];
}

export interface ReactionUpdate {
  id: string;
  status: ReactionStatus;
  notes?: string;
  assignedCounsellorId?: string;
}

export interface ReactionStats {
  total: number;
  pending: number;
  resolved: number;
  bySeverity: {
    mild: number;
    moderate: number;
    severe: number;
  };
}

export interface ReactionFilters {
  status?: ReactionStatus;
  severity?: ReactionSeverity;
  dateRange?: {
    start: string;
    end: string;
  };
  assignedCounsellorId?: string;
}
