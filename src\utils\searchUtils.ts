/**
 * Enhanced search utility functions for the Adverse Drug Reaction Reporting System
 * Handles full name searches with spaces, partial matches, and case-insensitive searches
 */

export interface SearchableUser {
  firstName?: string;
  lastName?: string;
  name?: string;
  email?: string;
  hospitalNo?: string;
  contactNumber?: string;
}

/**
 * Enhanced search function that handles various search patterns
 * @param items - Array of searchable items
 * @param searchQuery - Search query string
 * @param additionalFields - Additional fields to search in (optional)
 * @returns Filtered array of items matching the search query
 */
export function searchUsers<T extends SearchableUser>(
  items: T[],
  searchQuery: string,
  additionalFields?: (keyof T)[]
): T[] {
  if (!searchQuery.trim()) return items;

  const query = searchQuery.toLowerCase().trim();

  return items.filter(item => {
    // Extract and normalize basic fields
    const firstName = (item.firstName || '').toLowerCase();
    const lastName = (item.lastName || '').toLowerCase();
    const fullName = item.name ? item.name.toLowerCase() : `${firstName} ${lastName}`.trim();
    const email = (item.email || '').toLowerCase();
    const hospitalNo = (item.hospitalNo || '').toLowerCase();
    const contactNumber = (item.contactNumber || '').toLowerCase();

    // Basic search patterns
    const basicMatches = [
      // Full name match (handles "John Doe")
      fullName.includes(query),
      // Individual name matches
      firstName.includes(query),
      lastName.includes(query),
      // Email match
      email.includes(query),
      // Hospital number match
      hospitalNo.includes(query),
      // Contact number match
      contactNumber.includes(query),
      // Reverse name order match (handles "Doe John")
      `${lastName} ${firstName}`.trim().includes(query)
    ];

    // Check if any basic pattern matches
    if (basicMatches.some(match => match)) {
      return true;
    }

    // Advanced pattern: Handle partial matches with spaces (e.g., "Jo Do" matching "John Doe")
    const queryParts = query.split(' ').filter(part => part.trim());
    if (queryParts.length > 1) {
      const allPartsMatch = queryParts.every(queryPart => {
        const trimmedPart = queryPart.trim();
        return (
          firstName.includes(trimmedPart) ||
          lastName.includes(trimmedPart) ||
          fullName.includes(trimmedPart) ||
          email.includes(trimmedPart) ||
          hospitalNo.includes(trimmedPart) ||
          contactNumber.includes(trimmedPart)
        );
      });
      
      if (allPartsMatch) {
        return true;
      }
    }

    // Search in additional fields if provided
    if (additionalFields && additionalFields.length > 0) {
      return additionalFields.some(field => {
        const fieldValue = item[field];
        if (typeof fieldValue === 'string') {
          return fieldValue.toLowerCase().includes(query);
        }
        return false;
      });
    }

    return false;
  });
}

/**
 * Simple search function for basic text matching
 * @param items - Array of items with searchable text fields
 * @param searchQuery - Search query string
 * @param searchFields - Array of field names to search in
 * @returns Filtered array of items
 */
export function searchByFields<T>(
  items: T[],
  searchQuery: string,
  searchFields: (keyof T)[]
): T[] {
  if (!searchQuery.trim()) return items;

  const query = searchQuery.toLowerCase().trim();

  return items.filter(item => {
    return searchFields.some(field => {
      const fieldValue = item[field];
      if (typeof fieldValue === 'string') {
        return fieldValue.toLowerCase().includes(query);
      }
      return false;
    });
  });
}

/**
 * Search function specifically for chat messages
 * @param messages - Array of messages
 * @param searchQuery - Search query string
 * @returns Filtered array of messages
 */
export function searchMessages<T extends { text: string }>(
  messages: T[],
  searchQuery: string
): T[] {
  if (!searchQuery.trim()) return messages;

  const query = searchQuery.toLowerCase().trim();

  return messages.filter(message => 
    message.text.toLowerCase().includes(query)
  );
}

/**
 * Highlight search terms in text (for UI display)
 * @param text - Original text
 * @param searchQuery - Search query to highlight
 * @returns Object with highlighted text and whether matches were found
 */
export function highlightSearchTerms(text: string, searchQuery: string): {
  highlightedText: string;
  hasMatches: boolean;
} {
  if (!searchQuery.trim()) {
    return { highlightedText: text, hasMatches: false };
  }

  const query = searchQuery.trim();
  const regex = new RegExp(`(${query})`, 'gi');
  const hasMatches = regex.test(text);
  
  if (hasMatches) {
    const highlightedText = text.replace(regex, '<mark>$1</mark>');
    return { highlightedText, hasMatches: true };
  }

  return { highlightedText: text, hasMatches: false };
}

/**
 * Debounced search function to improve performance
 * @param searchFunction - The search function to debounce
 * @param delay - Delay in milliseconds (default: 300ms)
 * @returns Debounced search function
 */
export function debounceSearch<T extends any[]>(
  searchFunction: (...args: T) => void,
  delay: number = 300
): (...args: T) => void {
  let timeoutId: NodeJS.Timeout;

  return (...args: T) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => searchFunction(...args), delay);
  };
}
